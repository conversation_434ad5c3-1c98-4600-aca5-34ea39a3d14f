{% extends "base.html" %}

{% block title %}Edit Quiz: {{ quiz.title }}{% endblock %}

{% block content %}
<div class="container create-quiz-container">
    <h1>Edit Quiz: {{ quiz.title }}</h1>

    <!-- Version Warning -->
    {% if quiz.attempt_count and quiz.attempt_count > 0 %}
    <div class="warning-section">
        <div class="warning-card">
            <h3>⚠️ Important Notice</h3>
            <p>This quiz has <strong>{{ quiz.attempt_count }} existing attempts</strong>. Editing this quiz will:</p>
            <ul>
                <li>Create a new version (v{{ quiz.version_number + 1 }}) with your changes</li>
                <li>Lock the current version to preserve existing results</li>
                <li>Make the new version active for future attempts</li>
            </ul>
            <p><strong>The original quiz and all existing attempts will remain unchanged.</strong></p>
        </div>
    </div>
    {% endif %}

    <form method="post" action="{{ url_for('edit_quiz', quiz_id=quiz.id) }}" id="edit-quiz-form">
        <!-- Quiz Details Section -->
        <div class="form-section quiz-details-section">
            <h2>Quiz Details</h2>
            <div class="form-group">
                <label for="quiz_title">Quiz Title:</label>
                <input type="text" id="quiz_title" name="quiz_title" value="{{ quiz.title }}" required>
            </div>
            <div class="form-group">
                <label for="quiz_description">Description (Optional):</label>
                <textarea id="quiz_description" name="quiz_description" rows="3">{{ quiz.description }}</textarea>
            </div>
             <div class="form-row">
                <div class="form-group half-width">
                    <label for="time_limit">Time Limit (Minutes):</label>
                    <input type="number" id="time_limit" name="time_limit" value="{{ quiz.time_limit }}" min="1" required>
                </div>
                 <div class="form-group half-width">
                    <label for="difficulty">Difficulty:</label>
                    <select id="difficulty" name="difficulty" required>
                        <option value="easy" {% if quiz.difficulty == 'easy' %}selected{% endif %}>Easy</option>
                        <option value="medium" {% if quiz.difficulty == 'medium' %}selected{% endif %}>Medium</option>
                        <option value="hard" {% if quiz.difficulty == 'hard' %}selected{% endif %}>Hard</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                 <label class="group-label">Grading Thresholds (%):</label>
                 <div class="form-row grading-row">
                     <div class="grade-input">
                         <label for="grade_a">A ≥</label>
                         <input type="number" id="grade_a" name="grade_a" value="{{ quiz.grade_a_threshold }}" min="0" max="100" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_b">B ≥</label>
                         <input type="number" id="grade_b" name="grade_b" value="{{ quiz.grade_b_threshold }}" min="0" max="100" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_c">C ≥</label>
                         <input type="number" id="grade_c" name="grade_c" value="{{ quiz.grade_c_threshold }}" min="0" max="100" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_d">D ≥</label>
                         <input type="number" id="grade_d" name="grade_d" value="{{ quiz.grade_d_threshold }}" min="0" max="100" required>
                     </div>
                 </div>
            </div>

            <!-- Question Randomization Section -->
            <div class="form-group randomization-section">
                <label class="group-label">Question Display Options:</label>
                <div class="checkbox-group">
                    <input type="checkbox" id="randomize_questions" name="randomize_questions" value="1"
                           {% if quiz.randomize_questions %}checked{% endif %}>
                    <label for="randomize_questions" class="checkbox-label">
                        <span class="checkbox-text">Randomize question order for each student</span>
                        <span class="checkbox-description">When enabled, each student will see the same questions but in a different random order. This helps prevent cheating while maintaining fairness.</span>
                    </label>
                </div>
            </div>

            <!-- Calculator Access Section -->
            <div class="form-group calculator-section">
                <label class="group-label">Calculator Access:</label>
                <div class="checkbox-group">
                    <input type="checkbox" id="allow_calculator" name="allow_calculator" value="1"
                           {% if quiz.allow_calculator %}checked{% endif %}>
                    <label for="allow_calculator" class="checkbox-label">
                        <span class="checkbox-text">Allow students to use calculator during quiz</span>
                        <span class="checkbox-description">When enabled, students will have access to a built-in calculator tool while taking the quiz. The calculator is fully client-side and secure.</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Questions Section -->
        <div class="form-section questions-section">
            <h2>Questions</h2>
            <div id="questions-container">
                {% for question in questions %}
                <div class="question-card" data-question-index="{{ loop.index0 }}">
                     <div class="question-card-header">
                        <span class="question-card-number">Question {{ loop.index }}</span>
                        <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
                     </div>
                    <div class="form-group">
                        <textarea name="question[]" rows="2" required placeholder="Enter question text...">{{ question.question_text }}</textarea>
                        <input type="hidden" name="question_type[]" value="mcq"> <!-- Always MCQ -->
                    </div>
                    <div class="form-row question-card-footer">
                        <!-- Type dropdown removed -->
                        <div class="form-group question-meta marks-only">
                            <label>Marks:</label>
                            <input type="number" name="question_marks[]" value="{{ question.marks }}" min="1" required class="marks-input" onchange="updateTotalMarks()">
                        </div>
                    </div>

                    <!-- MCQ Fields -->
                    <div class="mcq-fields">
                        <label class="group-label">Options & Correct Answer:</label>
                        <div class="option-group">
                            <span class="option-marker">A</span>
                            <input type="text" name="option1[]" placeholder="Option 1" value="{{ question.option1 }}" required>
                            <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="1" {% if question.correct_answer == '1' %}checked{% endif %} required title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">B</span>
                            <input type="text" name="option2[]" placeholder="Option 2" value="{{ question.option2 }}" required>
                            <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="2" {% if question.correct_answer == '2' %}checked{% endif %} title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">C</span>
                            <input type="text" name="option3[]" placeholder="Option 3" value="{{ question.option3 }}">
                             <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="3" {% if question.correct_answer == '3' %}checked{% endif %} title="Mark as correct">
                        </div>
                        <div class="option-group">
                             <span class="option-marker">D</span>
                            <input type="text" name="option4[]" placeholder="Option 4" value="{{ question.option4 }}">
                             <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="4" {% if question.correct_answer == '4' %}checked{% endif %} title="Mark as correct">
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <button type="button" class="btn btn-outline" id="add-question-btn">+ Add Another Question</button>
        </div>
        
        <input type="hidden" id="total_marks" name="total_marks" value="{{ quiz.total_marks }}">
        <button type="submit" class="btn btn-primary btn-submit-quiz">Update Quiz</button>
    </form>
</div>

<!-- Reuse script from create_quiz.html, ensuring it handles initial data correctly -->
<script>
// Initialize questionIndex based on existing questions
let questionIndex = {{ questions|length }};

function addQuestion() {
    const container = document.getElementById('questions-container');
    const newQuestionCard = document.createElement('div');
    newQuestionCard.classList.add('question-card');
    newQuestionCard.dataset.questionIndex = questionIndex;
    const currentQuestionNumber = container.querySelectorAll('.question-card').length + 1; // Use current count + 1 for numbering
    
    newQuestionCard.innerHTML = `
        <div class="question-card-header">
            <span class="question-card-number">Question ${currentQuestionNumber}</span>
            <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
        </div>
        <div class="form-group">
            <textarea name="question[]" rows="2" required placeholder="Enter question text..."></textarea>
            <input type="hidden" name="question_type[]" value="mcq">
        </div>
        <div class="form-row question-card-footer">
            <div class="form-group question-meta marks-only">
                <label>Marks:</label>
                <input type="number" name="question_marks[]" min="1" value="1" required class="marks-input" onchange="updateTotalMarks()">
            </div>
        </div>
        <div class="mcq-fields">
             <label class="group-label">Options & Correct Answer:</label>
             <div class="option-group">
                 <span class="option-marker">A</span>
                <input type="text" name="option1[]" placeholder="Option 1" required>
                <input type="radio" name="correct_answer[${questionIndex}]" value="1" required title="Mark as correct">
             </div>
             <div class="option-group">
                 <span class="option-marker">B</span>
                <input type="text" name="option2[]" placeholder="Option 2" required>
                 <input type="radio" name="correct_answer[${questionIndex}]" value="2" title="Mark as correct">
             </div>
            <div class="option-group">
                 <span class="option-marker">C</span>
                 <input type="text" name="option3[]" placeholder="Option 3">
                 <input type="radio" name="correct_answer[${questionIndex}]" value="3" title="Mark as correct">
             </div>
             <div class="option-group">
                  <span class="option-marker">D</span>
                 <input type="text" name="option4[]" placeholder="Option 4">
                 <input type="radio" name="correct_answer[${questionIndex}]" value="4" title="Mark as correct">
             </div>
        </div>
    `;
    container.appendChild(newQuestionCard);
    questionIndex++; // Increment the global index AFTER setting the name for the new card
    updateTotalMarks();
}

function removeQuestion(button) {
    const questionCard = button.closest('.question-card');
    questionCard.remove();
    // Renumber remaining questions
    const cards = document.querySelectorAll('#questions-container .question-card');
    cards.forEach((card, index) => {
        card.querySelector('.question-card-number').textContent = `Question ${index + 1}`;
        const radios = card.querySelectorAll('input[type="radio"]');
        // Ensure radio button names use the correct *new* index
        radios.forEach(radio => {
             radio.name = `correct_answer[${index}]`;
        });
        card.dataset.questionIndex = index; // Update the index stored on the element
    });
    // Update the global index to reflect the new count
    questionIndex = cards.length;
    updateTotalMarks();
}

function updateTotalMarks() {
    let total = 0;
    const marksInputs = document.querySelectorAll('.marks-input');
    marksInputs.forEach(input => {
        total += parseInt(input.value) || 0;
    });
    document.getElementById('total_marks').value = total;
}

document.getElementById('add-question-btn').addEventListener('click', addQuestion);

// Initial setup for existing questions
document.addEventListener('DOMContentLoaded', () => {
    updateTotalMarks();
});
</script>

<!-- Link to the same styles as create_quiz.html or copy relevant styles -->
<style>
/* Copy or link styles from create_quiz.html */
/* Improved styling for create quiz page */
body {
    background-color: #f4f7f6; /* Lighter page background */
}

.create-quiz-container {
    max-width: 950px;
    margin: 2rem auto;
    background-color: transparent; /* Container is just for max-width */
    padding: 0; /* Remove padding from main container */
}

.create-quiz-container h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

/* Style for form sections (details, questions) */
.form-section {
    background-color: #fff;
    padding: 2rem 2.5rem;
    border-radius: 10px;
    margin-bottom: 2.5rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
}

.form-section h2 {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.8rem;
    margin-bottom: 2rem;
    color: #007bff; /* Theme color for headings */
    font-size: 1.6rem;
}

/* General form group styling */
.form-group {
    margin-bottom: 1.5rem;
}

/* Randomization Section Styling */
.randomization-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-group input[type="checkbox"] {
    margin-top: 0.25rem;
    transform: scale(1.2);
    cursor: pointer;
}

.checkbox-label {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.checkbox-text {
    font-weight: 600;
    color: #495057;
}

.checkbox-description {
    font-size: 0.9em;
    color: #6c757d;
    line-height: 1.4;
}

label {
    display: block;
    margin-bottom: 0.6rem;
    color: #495057;
    font-weight: 600; /* Slightly bolder labels */
    font-size: 0.95rem;
}

label.group-label {
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #343a40;
}

input[type="text"],
input[type="number"],
textarea,
select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 5px;
    box-sizing: border-box;
    font-size: 1rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea {
    resize: vertical;
    min-height: 70px;
}

select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem; /* Make space for arrow */
}

/* Layout helpers */
.form-row {
    display: flex;
    gap: 2rem;
    align-items: flex-end; /* Align items based on their bottom edge */
    margin-bottom: 1.5rem;
}

.form-group.half-width {
    flex: 1 1 50%; /* Allow shrinking but prefer 50% */
}

.grading-row {
    justify-content: space-around;
    gap: 1rem;
}

.grade-input {
     display: flex;
     flex-direction: column; /* Stack label and input */
     align-items: center;
     gap: 0.3rem;
     flex: 1;
     min-width: 60px; /* Prevent shrinking too much */
}
.grade-input label {
    margin-bottom: 0;
    white-space: nowrap;
    font-weight: normal;
    font-size: 0.9rem;
}
.grade-input input {
     width: 100%;
     text-align: center;
}

/* Question Card Styling */
.question-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    position: relative; 
    background-color: #fff;
    border-left: 4px solid #007bff; /* Accent border */
}

.question-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.question-card-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #0056b3;
}

.remove-question-btn {
    background: #f1f3f5;
    color: #dc3545;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 1.4rem;
    font-weight: bold;
    line-height: 26px; 
    text-align: center;
    cursor: pointer;
    padding: 0;
    transition: background-color 0.2s, color 0.2s;
}
.remove-question-btn:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.question-card-footer {
    justify-content: flex-end; /* Push marks to the right */
}

.question-meta.marks-only {
   flex: 0 1 150px; /* Give marks input a reasonable max width */
   max-width: 150px; /* Adjust as needed */
}

/* MCQ Options Styling */
.mcq-fields {
    margin-top: 1.5rem;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
    padding-left: 10px; /* Indent options slightly */
}

.option-marker {
    font-weight: bold;
    color: #6c757d;
    width: 20px; /* Fixed width for alignment */
    text-align: right;
}

.option-group input[type="text"] {
    flex-grow: 1;
}

.option-group input[type="radio"] {
    width: auto;
    margin-left: 10px;
    cursor: pointer;
    transform: scale(1.2); /* Make radio slightly larger */
}

/* Button Styling */
.btn {
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    border: 1px solid transparent;
}

#add-question-btn {
    display: block;
    margin: 1.5rem auto 0 auto; /* Center add button */
}

.btn-primary {
    background-color: #007bff; /* Blue for update */
    color: white;
    border-color: #007bff;
}
.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-outline {
    background-color: transparent;
    color: #007bff;
    border-color: #007bff;
}
.btn-outline:hover {
    background-color: #007bff;
    color: white;
}

.btn-submit-quiz {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    margin-top: 2rem;
}

</style>
{% endblock %} 