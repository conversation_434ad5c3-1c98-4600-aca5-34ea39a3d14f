#!/usr/bin/env python3
"""
Migration script to add ParentComment table for parent feedback system.
This script creates the parent_comment table with all necessary fields and relationships.
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_parent_comments():
    """Create ParentComment table for parent feedback system"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure you're running this script from the correct directory.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting parent comments migration...")
        print("=" * 50)
        
        # Check if parent_comment table already exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='parent_comment'
        """)
        
        if cursor.fetchone():
            print("ParentComment table already exists. No migration needed.")
            return
        
        print("Creating parent_comment table...")
        
        # Create parent_comment table
        cursor.execute("""
            CREATE TABLE parent_comment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                attempt_id INTEGER NOT NULL,
                parent_id INTEGER NOT NULL,
                comment_text TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                teacher_response TEXT,
                teacher_response_timestamp DATETIME,
                teacher_id INTEGER,
                FOREIGN KEY (attempt_id) REFERENCES quiz_attempt (id) ON DELETE CASCADE,
                FOREIGN KEY (parent_id) REFERENCES user (id) ON DELETE CASCADE,
                FOREIGN KEY (teacher_id) REFERENCES user (id) ON DELETE SET NULL
            )
        """)
        
        print("✓ Created parent_comment table")
        
        # Create indexes for better performance
        cursor.execute("""
            CREATE INDEX idx_parent_comment_attempt_id ON parent_comment(attempt_id)
        """)
        print("✓ Created index on attempt_id")
        
        cursor.execute("""
            CREATE INDEX idx_parent_comment_parent_id ON parent_comment(parent_id)
        """)
        print("✓ Created index on parent_id")
        
        cursor.execute("""
            CREATE INDEX idx_parent_comment_timestamp ON parent_comment(timestamp)
        """)
        print("✓ Created index on timestamp")
        
        cursor.execute("""
            CREATE INDEX idx_parent_comment_teacher_id ON parent_comment(teacher_id)
        """)
        print("✓ Created index on teacher_id")
        
        conn.commit()
        print("\n✓ Database migration completed successfully!")
        print("\nParent comment system is now enabled:")
        print("- Parents can leave comments on their children's quiz attempts")
        print("- Teachers can view and respond to parent comments")
        print("- Comments are linked to specific quiz attempts")
        print("- Role-based access control ensures proper permissions")
        print("- Email notifications alert teachers of new comments")
        
        # Show table structure
        cursor.execute("PRAGMA table_info(parent_comment)")
        columns = cursor.fetchall()
        
        print(f"\nParentComment table structure:")
        for column in columns:
            print(f"- {column[1]} ({column[2]})")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nVerifying migration...")
        
        # Check if table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='parent_comment'
        """)
        
        if not cursor.fetchone():
            print("❌ Migration verification failed!")
            print("ParentComment table was not created.")
            return False
        
        # Check table structure
        cursor.execute("PRAGMA table_info(parent_comment)")
        columns = cursor.fetchall()
        
        required_columns = [
            'id', 'attempt_id', 'parent_id', 'comment_text', 
            'timestamp', 'teacher_response', 'teacher_response_timestamp', 'teacher_id'
        ]
        
        existing_columns = [column[1] for column in columns]
        missing_columns = [col for col in required_columns if col not in existing_columns]
        
        if missing_columns:
            print("❌ Migration verification failed!")
            print(f"Missing columns: {missing_columns}")
            return False
        
        # Check indexes
        cursor.execute("PRAGMA index_list(parent_comment)")
        indexes = cursor.fetchall()
        
        expected_indexes = [
            'idx_parent_comment_attempt_id',
            'idx_parent_comment_parent_id', 
            'idx_parent_comment_timestamp',
            'idx_parent_comment_teacher_id'
        ]
        
        existing_indexes = [index[1] for index in indexes]
        missing_indexes = [idx for idx in expected_indexes if idx not in existing_indexes]
        
        if missing_indexes:
            print("⚠️ Some indexes are missing (performance may be affected):")
            print(f"Missing indexes: {missing_indexes}")
        
        print("✅ Migration verification successful!")
        print("ParentComment table is properly configured.")
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_parent_comment_functionality():
    """Test basic parent comment functionality"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nTesting parent comment functionality...")
        
        # Test inserting a sample comment (will be rolled back)
        cursor.execute("""
            INSERT INTO parent_comment (attempt_id, parent_id, comment_text)
            VALUES (999999, 999999, 'Test comment for migration verification')
        """)
        
        # Test querying the comment
        cursor.execute("""
            SELECT id, comment_text FROM parent_comment 
            WHERE comment_text = 'Test comment for migration verification'
        """)
        
        result = cursor.fetchone()
        if result:
            print("✓ Insert and query operations work correctly")
            
            # Test updating with teacher response
            cursor.execute("""
                UPDATE parent_comment 
                SET teacher_response = 'Test teacher response',
                    teacher_response_timestamp = CURRENT_TIMESTAMP,
                    teacher_id = 999999
                WHERE id = ?
            """, (result[0],))
            
            print("✓ Update operations work correctly")
            
            # Clean up test data
            cursor.execute("DELETE FROM parent_comment WHERE id = ?", (result[0],))
            print("✓ Delete operations work correctly")
            
        conn.rollback()  # Don't actually save test data
        print("✅ All basic operations are functional!")
        
    except Exception as e:
        print(f"❌ Error during functionality test: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Parent Comments Migration")
    print("=" * 50)
    migrate_parent_comments()
    verify_migration()
    test_parent_comment_functionality()
    print("=" * 50)
    print("Migration completed!")
