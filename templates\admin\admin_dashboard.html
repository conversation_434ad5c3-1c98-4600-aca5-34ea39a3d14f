{% extends "base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-modern.css') }}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- Modern Header -->
    <div class="admin-header">
        <div>
            <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
            <div class="subtitle">Comprehensive system management and analytics</div>
        </div>
        <div class="header-actions">
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline">
                <i class="fas fa-home"></i> Main Dashboard
            </a>
            <a href="{{ url_for('logout') }}" class="btn btn-secondary">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>

    <!-- Quick Stats Overview -->
    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value" id="total-users">-</div>
            <div class="stat-label">Total Users</div>
        </div>
        <div class="stat-card success">
            <div class="stat-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="stat-value" id="total-quizzes">-</div>
            <div class="stat-label">Total Quizzes</div>
        </div>
        <div class="stat-card warning">
            <div class="stat-icon">
                <i class="fas fa-pencil-alt"></i>
            </div>
            <div class="stat-value" id="total-attempts">-</div>
            <div class="stat-label">Quiz Attempts</div>
        </div>
        <div class="stat-card info">
            <div class="stat-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-value" id="pending-users">-</div>
            <div class="stat-label">Pending Users</div>
        </div>
    </div>

    <!-- Main Admin Grid -->
    <div class="admin-grid">
        <!-- User Management -->
        <div class="admin-card">
            <div class="card-header">
                <h3><i class="fas fa-users-cog"></i> User Management</h3>
            </div>
            <div class="card-body">
                <p>Manage users, roles, and permissions across the system</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_manage_users') }}" class="btn btn-primary">
                        <i class="fas fa-users"></i> Manage Users
                    </a>
                    <a href="{{ url_for('admin_pending_users') }}" class="btn btn-warning">
                        <i class="fas fa-user-clock"></i> Pending Users
                    </a>
                </div>
            </div>
        </div>

        <!-- Quiz Management -->
        <div class="admin-card">
            <div class="card-header">
                <h3><i class="fas fa-clipboard-list"></i> Quiz Management</h3>
            </div>
            <div class="card-body">
                <p>View, edit, and manage all quizzes in the system</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_manage_quizzes') }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Manage Quizzes
                    </a>
                    <a href="{{ url_for('admin_quiz_attempts') }}" class="btn btn-info">
                        <i class="fas fa-chart-line"></i> View Attempts
                    </a>
                </div>
            </div>
        </div>

        <!-- Analytics & Reports -->
        <div class="admin-card">
            <div class="card-header">
                <h3><i class="fas fa-chart-bar"></i> Analytics & Reports</h3>
            </div>
            <div class="card-body">
                <p>Comprehensive system statistics and performance metrics</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_statistics') }}" class="btn btn-success">
                        <i class="fas fa-chart-pie"></i> View Statistics
                    </a>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="admin-card">
            <div class="card-header">
                <h3><i class="fas fa-cogs"></i> System Settings</h3>
            </div>
            <div class="card-body">
                <p>Configure system-wide settings and preferences</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_settings') }}" class="btn btn-secondary">
                        <i class="fas fa-sliders-h"></i> Manage Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load dashboard statistics
document.addEventListener('DOMContentLoaded', function() {
    // Fetch statistics from the server
    fetch('/admin/api/dashboard-stats')
        .then(response => response.json())
        .then(data => {
            // Update the dashboard with the fetched data
            document.getElementById('total-users').textContent = data.total_users || '0';
            document.getElementById('total-quizzes').textContent = data.total_quizzes || '0';
            document.getElementById('total-attempts').textContent = data.total_attempts || '0';
            document.getElementById('pending-users').textContent = data.pending_users || '0';
        })
        .catch(error => {
            console.error('Error fetching dashboard stats:', error);
            // Fallback to default values
            document.getElementById('total-users').textContent = '{{ user_counts.admin + user_counts.teacher + user_counts.student + user_counts.parent if user_counts else "0" }}';
            document.getElementById('total-quizzes').textContent = '{{ total_quizzes if total_quizzes else "0" }}';
            document.getElementById('total-attempts').textContent = '{{ total_attempts if total_attempts else "0" }}';
            document.getElementById('pending-users').textContent = '{{ pending_users if pending_users else "0" }}';
        });
});
</script>
{% endblock %}
