#!/usr/bin/env python3
"""
Test script for calculator feature in quiz system.
This script validates that the calculator feature works correctly with all functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, Question, User
from werkzeug.security import generate_password_hash
import uuid

def test_calculator_feature():
    """Test the calculator feature in quiz system"""
    with app.app_context():
        print("Testing Calculator Feature in Quiz System")
        print("=" * 50)
        
        # Create test teacher
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Calculator Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
        
        # Create test student
        student = User.query.filter_by(email='<EMAIL>').first()
        if not student:
            student = User(
                name='Test Calculator Student',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                is_verified=True
            )
            db.session.add(student)
        
        db.session.commit()
        print("✓ Created test users (teacher, student)")
        
        # Test 1: Create quiz with calculator enabled
        print("\nTest 1: Creating quiz with calculator enabled...")
        quiz_with_calc = Quiz(
            title='Test Quiz with Calculator',
            description='A test quiz to validate calculator feature',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False,
            allow_calculator=True
        )
        db.session.add(quiz_with_calc)
        db.session.flush()
        
        # Add test questions
        questions_with_calc = []
        for i in range(3):
            question = Question(
                quiz_id=quiz_with_calc.id,
                question_text=f'Math Question {i+1}: Calculate {(i+1)*5} × {(i+1)*3} + {(i+1)*2}',
                question_type='mcq',
                option1=f'{(i+1)*5 * (i+1)*3 + (i+1)*2 - 5}',
                option2=f'{(i+1)*5 * (i+1)*3 + (i+1)*2}',
                option3=f'{(i+1)*5 * (i+1)*3 + (i+1)*2 + 5}',
                option4=f'{(i+1)*5 * (i+1)*3 + (i+1)*2 + 10}',
                correct_answer='2',
                marks=33
            )
            questions_with_calc.append(question)
            db.session.add(question)
        
        db.session.commit()
        print(f"✓ Created quiz with calculator enabled (ID: {quiz_with_calc.id})")
        
        # Test 2: Create quiz with calculator disabled
        print("\nTest 2: Creating quiz with calculator disabled...")
        quiz_without_calc = Quiz(
            title='Test Quiz without Calculator',
            description='A test quiz without calculator access',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False,
            allow_calculator=False
        )
        db.session.add(quiz_without_calc)
        db.session.flush()
        
        # Add test questions
        questions_without_calc = []
        for i in range(3):
            question = Question(
                quiz_id=quiz_without_calc.id,
                question_text=f'Simple Question {i+1}: What is {i+1} + {i+1}?',
                question_type='mcq',
                option1=f'{i}',
                option2=f'{(i+1)*2}',
                option3=f'{i+3}',
                option4=f'{i+5}',
                correct_answer='2',
                marks=33
            )
            questions_without_calc.append(question)
            db.session.add(question)
        
        db.session.commit()
        print(f"✓ Created quiz without calculator (ID: {quiz_without_calc.id})")
        
        # Test 3: Verify calculator field functionality
        print("\nTest 3: Testing calculator field functionality...")
        
        # Test quiz with calculator enabled
        assert quiz_with_calc.allow_calculator == True, "Quiz should have calculator enabled"
        print("✓ Calculator enabled quiz field works correctly")
        
        # Test quiz with calculator disabled
        assert quiz_without_calc.allow_calculator == False, "Quiz should have calculator disabled"
        print("✓ Calculator disabled quiz field works correctly")
        
        # Test 4: Test default calculator setting
        print("\nTest 4: Testing default calculator setting...")
        
        # Create quiz without explicitly setting calculator
        default_quiz = Quiz(
            title='Test Quiz with Default Settings',
            description='A test quiz with default calculator setting',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False
            # allow_calculator not explicitly set, should default to True
        )
        db.session.add(default_quiz)
        db.session.commit()
        
        # Refresh from database to get default value
        db.session.refresh(default_quiz)
        assert default_quiz.allow_calculator == True, "Quiz should have calculator enabled by default"
        print("✓ Default calculator setting works correctly")
        
        # Test 5: Test calculator setting updates
        print("\nTest 5: Testing calculator setting updates...")
        
        # Update calculator setting
        original_setting = quiz_with_calc.allow_calculator
        quiz_with_calc.allow_calculator = False
        db.session.commit()
        
        # Verify update
        updated_quiz = Quiz.query.get(quiz_with_calc.id)
        assert updated_quiz.allow_calculator == False, "Calculator setting should be updated"
        print("✓ Calculator setting update works correctly")
        
        # Restore original setting
        quiz_with_calc.allow_calculator = original_setting
        db.session.commit()
        
        # Test 6: Test quiz queries with calculator filter
        print("\nTest 6: Testing quiz queries with calculator filter...")
        
        # Query quizzes with calculator enabled
        calc_enabled_quizzes = Quiz.query.filter_by(allow_calculator=True).all()
        calc_enabled_count = len(calc_enabled_quizzes)
        
        # Query quizzes with calculator disabled
        calc_disabled_quizzes = Quiz.query.filter_by(allow_calculator=False).all()
        calc_disabled_count = len(calc_disabled_quizzes)
        
        print(f"✓ Found {calc_enabled_count} quizzes with calculator enabled")
        print(f"✓ Found {calc_disabled_count} quizzes with calculator disabled")
        
        # Test 7: Test calculator field in different quiz contexts
        print("\nTest 7: Testing calculator field in different contexts...")
        
        # Test in teacher's quiz list
        teacher_quizzes = Quiz.query.filter_by(teacher_id=teacher.id).all()
        for quiz in teacher_quizzes:
            assert hasattr(quiz, 'allow_calculator'), "Quiz should have allow_calculator attribute"
            assert isinstance(quiz.allow_calculator, bool), "allow_calculator should be boolean"
        
        print("✓ Calculator field works correctly in teacher quiz lists")
        
        # Test 8: Test data integrity
        print("\nTest 8: Testing data integrity...")
        
        # Test that calculator setting is preserved
        quiz_id = quiz_with_calc.id
        calc_setting = quiz_with_calc.allow_calculator
        
        # Reload quiz from database
        reloaded_quiz = Quiz.query.get(quiz_id)
        assert reloaded_quiz.allow_calculator == calc_setting, "Calculator setting should be preserved"
        print("✓ Calculator setting data integrity verified")
        
        # Test 9: Test calculator field with quiz operations
        print("\nTest 9: Testing calculator field with quiz operations...")
        
        # Test quiz creation with calculator setting
        test_quiz_data = {
            'title': 'Calculator Test Quiz',
            'description': 'Testing calculator field',
            'teacher_id': teacher.id,
            'time_limit': 30,
            'total_marks': 100,
            'grade_a_threshold': 90,
            'grade_b_threshold': 80,
            'grade_c_threshold': 70,
            'grade_d_threshold': 60,
            'difficulty': 'easy',
            'allow_calculator': True
        }
        
        operation_test_quiz = Quiz(**test_quiz_data)
        db.session.add(operation_test_quiz)
        db.session.commit()
        
        assert operation_test_quiz.allow_calculator == True, "Calculator setting should be preserved in operations"
        print("✓ Calculator field works correctly with quiz operations")
        
        # Test 10: Test edge cases
        print("\nTest 10: Testing edge cases...")
        
        # Test with None value (should use default)
        edge_case_quiz = Quiz(
            title='Edge Case Quiz',
            description='Testing edge cases',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='hard'
            # allow_calculator intentionally omitted
        )
        db.session.add(edge_case_quiz)
        db.session.commit()
        
        # Should use default value
        db.session.refresh(edge_case_quiz)
        assert edge_case_quiz.allow_calculator is not None, "Calculator field should have default value"
        print("✓ Edge case handling works correctly")
        
        # Cleanup
        print("\nCleaning up test data...")
        try:
            # Delete questions first (foreign key constraint)
            for question in questions_with_calc + questions_without_calc:
                db.session.delete(question)
            
            # Delete quizzes
            db.session.delete(quiz_with_calc)
            db.session.delete(quiz_without_calc)
            db.session.delete(default_quiz)
            db.session.delete(operation_test_quiz)
            db.session.delete(edge_case_quiz)
            
            # Delete test users
            db.session.delete(student)
            db.session.delete(teacher)
            
            db.session.commit()
            print("✓ Test data cleaned up successfully")
        except Exception as e:
            print(f"Warning: Could not clean up all test data: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All calculator feature tests passed!")
        print("\nSummary:")
        print("- Calculator field creation and storage works")
        print("- Default calculator setting (enabled) works")
        print("- Calculator enable/disable functionality works")
        print("- Quiz queries with calculator filter work")
        print("- Data integrity and persistence work")
        print("- Edge case handling works correctly")
        print("- Calculator field integrates properly with quiz operations")
        
        return True

if __name__ == "__main__":
    try:
        success = test_calculator_feature()
        if success:
            print("\n✅ Calculator feature test completed successfully!")
        else:
            print("\n❌ Calculator feature test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
