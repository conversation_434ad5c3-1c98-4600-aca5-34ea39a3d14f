#!/usr/bin/env python3
"""
Migration script to enhance messaging system with student-parent functionality.
This script adds new fields to the Message table to support enhanced messaging features.
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_enhanced_messaging():
    """Add enhanced messaging fields to Message table"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure you're running this script from the correct directory.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting enhanced messaging migration...")
        print("=" * 50)
        
        # Check if enhanced messaging columns already exist in message table
        cursor.execute("PRAGMA table_info(message)")
        message_columns = [column[1] for column in cursor.fetchall()]
        
        new_columns = ['is_parent_copy', 'original_recipient_id', 'message_group_id', 'receiver_role']
        existing_new_columns = [col for col in new_columns if col in message_columns]
        
        if len(existing_new_columns) == len(new_columns):
            print("Enhanced messaging fields already exist. No migration needed.")
            return
        
        print("Adding enhanced messaging fields to message table...")
        
        # Add is_parent_copy column
        if 'is_parent_copy' not in message_columns:
            cursor.execute("""
                ALTER TABLE message ADD COLUMN is_parent_copy BOOLEAN 
                DEFAULT 0 NOT NULL
            """)
            print("✓ Added is_parent_copy column to message table")
        
        # Add original_recipient_id column
        if 'original_recipient_id' not in message_columns:
            cursor.execute("""
                ALTER TABLE message ADD COLUMN original_recipient_id INTEGER 
                REFERENCES user(id)
            """)
            print("✓ Added original_recipient_id column to message table")
        
        # Add message_group_id column
        if 'message_group_id' not in message_columns:
            cursor.execute("""
                ALTER TABLE message ADD COLUMN message_group_id TEXT
            """)
            print("✓ Added message_group_id column to message table")
        
        # Add receiver_role column
        if 'receiver_role' not in message_columns:
            cursor.execute("""
                ALTER TABLE message ADD COLUMN receiver_role TEXT
            """)
            print("✓ Added receiver_role column to message table")
        
        # Update existing messages with receiver_role
        cursor.execute("""
            UPDATE message 
            SET receiver_role = (
                SELECT role FROM user WHERE user.id = message.receiver_id
            )
            WHERE receiver_role IS NULL
        """)
        print("✓ Updated existing messages with receiver roles")
        
        conn.commit()
        print("\n✓ Database migration completed successfully!")
        print("\nEnhanced messaging features are now enabled:")
        print("- Teachers can send messages to students and their parents simultaneously")
        print("- Messages are automatically copied to linked parents")
        print("- Privacy controls ensure parents and students only see their own messages")
        print("- Teachers can see enhanced message context and delivery information")
        print("- Message grouping allows tracking of related messages")
        
        # Show statistics
        cursor.execute("SELECT COUNT(*) FROM message")
        total_messages = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM message WHERE is_parent_copy = 1")
        parent_copy_messages = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT message_group_id) FROM message WHERE message_group_id IS NOT NULL")
        message_groups = cursor.fetchone()[0]
        
        print(f"\nDatabase Statistics:")
        print(f"- Total messages: {total_messages}")
        print(f"- Parent copy messages: {parent_copy_messages}")
        print(f"- Message groups: {message_groups}")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nVerifying migration...")
        
        # Check Message table structure
        cursor.execute("PRAGMA table_info(message)")
        message_columns = [column[1] for column in cursor.fetchall()]
        
        # Verify required columns exist
        required_columns = ['is_parent_copy', 'original_recipient_id', 'message_group_id', 'receiver_role']
        missing_columns = [col for col in required_columns if col not in message_columns]
        
        if missing_columns:
            print(f"❌ Migration verification failed!")
            print(f"Missing columns: {', '.join(missing_columns)}")
            return False
        
        # Check column properties
        cursor.execute("PRAGMA table_info(message)")
        columns = cursor.fetchall()
        
        print("✓ All enhanced messaging columns found:")
        for column in columns:
            if column[1] in required_columns:
                column_name = column[1]
                column_type = column[2]
                not_null = column[3]
                default_value = column[4]
                
                print(f"  - {column_name}: Type={column_type}, NotNull={bool(not_null)}, Default={default_value}")
        
        print("✅ Migration verification successful!")
        print("Enhanced messaging system is properly configured.")
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_enhanced_messaging():
    """Test basic enhanced messaging functionality"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nTesting enhanced messaging functionality...")
        
        # Test querying messages with enhanced fields
        cursor.execute("""
            SELECT id, sender_id, receiver_id, is_parent_copy, receiver_role 
            FROM message 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        if results:
            print("✓ Enhanced message fields query works correctly")
            print("Sample message data:")
            for msg_id, sender_id, receiver_id, is_parent_copy, receiver_role in results:
                copy_status = "Parent Copy" if is_parent_copy else "Direct Message"
                role = receiver_role or "Unknown"
                print(f"  - Message {msg_id}: {copy_status} to {role}")
        else:
            print("ℹ️ No messages found in database")
        
        # Test parent-student relationship queries
        cursor.execute("""
            SELECT COUNT(*) FROM user 
            WHERE role = 'student' AND parent_email IS NOT NULL
        """)
        
        students_with_parents = cursor.fetchone()[0]
        print(f"✓ Found {students_with_parents} students with linked parents")
        
        # Test message grouping functionality
        cursor.execute("""
            SELECT message_group_id, COUNT(*) as message_count
            FROM message 
            WHERE message_group_id IS NOT NULL
            GROUP BY message_group_id
            LIMIT 3
        """)
        
        group_results = cursor.fetchall()
        if group_results:
            print("✓ Message grouping functionality works correctly")
            print("Sample message groups:")
            for group_id, count in group_results:
                print(f"  - Group {group_id[:8]}...: {count} messages")
        
        print("✅ All basic operations are functional!")
        
    except Exception as e:
        print(f"❌ Error during functionality test: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Enhanced Messaging Migration")
    print("=" * 50)
    migrate_enhanced_messaging()
    verify_migration()
    test_enhanced_messaging()
    print("=" * 50)
    print("Migration completed!")
