#!/usr/bin/env python3
"""
Migration script to add enhanced report system tables.
This script creates the report_card_comment and quiz_feedback tables for the enhanced student report system.
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_enhanced_reports():
    """Create enhanced report system tables"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure you're running this script from the correct directory.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting enhanced reports migration...")
        print("=" * 50)
        
        # Check if tables already exist
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('report_card_comment', 'quiz_feedback')
        """)
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        tables_to_create = []
        if 'report_card_comment' not in existing_tables:
            tables_to_create.append('report_card_comment')
        if 'quiz_feedback' not in existing_tables:
            tables_to_create.append('quiz_feedback')
        
        if not tables_to_create:
            print("Enhanced report tables already exist. No migration needed.")
            return
        
        # Create report_card_comment table
        if 'report_card_comment' in tables_to_create:
            print("Creating report_card_comment table...")
            cursor.execute("""
                CREATE TABLE report_card_comment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    teacher_id INTEGER NOT NULL,
                    comment_text TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES user (id) ON DELETE CASCADE,
                    FOREIGN KEY (teacher_id) REFERENCES user (id) ON DELETE CASCADE
                )
            """)
            print("✓ Created report_card_comment table")
            
            # Create indexes for report_card_comment
            cursor.execute("""
                CREATE INDEX idx_report_card_comment_student_id ON report_card_comment(student_id)
            """)
            cursor.execute("""
                CREATE INDEX idx_report_card_comment_teacher_id ON report_card_comment(teacher_id)
            """)
            cursor.execute("""
                CREATE INDEX idx_report_card_comment_timestamp ON report_card_comment(timestamp)
            """)
            print("✓ Created indexes for report_card_comment table")
        
        # Create quiz_feedback table
        if 'quiz_feedback' in tables_to_create:
            print("Creating quiz_feedback table...")
            cursor.execute("""
                CREATE TABLE quiz_feedback (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    attempt_id INTEGER NOT NULL,
                    teacher_id INTEGER NOT NULL,
                    comment_text TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (attempt_id) REFERENCES quiz_attempt (id) ON DELETE CASCADE,
                    FOREIGN KEY (teacher_id) REFERENCES user (id) ON DELETE CASCADE
                )
            """)
            print("✓ Created quiz_feedback table")
            
            # Create indexes for quiz_feedback
            cursor.execute("""
                CREATE INDEX idx_quiz_feedback_attempt_id ON quiz_feedback(attempt_id)
            """)
            cursor.execute("""
                CREATE INDEX idx_quiz_feedback_teacher_id ON quiz_feedback(teacher_id)
            """)
            cursor.execute("""
                CREATE INDEX idx_quiz_feedback_timestamp ON quiz_feedback(timestamp)
            """)
            print("✓ Created indexes for quiz_feedback table")
        
        conn.commit()
        print("\n✓ Database migration completed successfully!")
        print("\nEnhanced report system is now enabled:")
        print("- Teachers can add overall report card comments for students")
        print("- Teachers can add specific feedback for individual quiz attempts")
        print("- Parents can view teacher feedback on their children's performance")
        print("- Students can see teacher feedback on their quiz attempts")
        print("- Email notifications alert parents when teachers add feedback")
        print("- Role-based access control ensures proper visibility")
        
        # Show table structures
        for table in tables_to_create:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            print(f"\n{table} table structure:")
            for column in columns:
                print(f"- {column[1]} ({column[2]})")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nVerifying migration...")
        
        # Check if tables exist
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('report_card_comment', 'quiz_feedback')
        """)
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['report_card_comment', 'quiz_feedback']
        missing_tables = [table for table in required_tables if table not in existing_tables]
        
        if missing_tables:
            print("❌ Migration verification failed!")
            print(f"Missing tables: {missing_tables}")
            return False
        
        # Check table structures
        for table in required_tables:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            if table == 'report_card_comment':
                required_columns = ['id', 'student_id', 'teacher_id', 'comment_text', 'timestamp']
            else:  # quiz_feedback
                required_columns = ['id', 'attempt_id', 'teacher_id', 'comment_text', 'timestamp']
            
            existing_columns = [column[1] for column in columns]
            missing_columns = [col for col in required_columns if col not in existing_columns]
            
            if missing_columns:
                print(f"❌ Migration verification failed for {table}!")
                print(f"Missing columns: {missing_columns}")
                return False
        
        # Check indexes
        expected_indexes = [
            'idx_report_card_comment_student_id',
            'idx_report_card_comment_teacher_id',
            'idx_report_card_comment_timestamp',
            'idx_quiz_feedback_attempt_id',
            'idx_quiz_feedback_teacher_id',
            'idx_quiz_feedback_timestamp'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
        existing_indexes = [row[0] for row in cursor.fetchall()]
        missing_indexes = [idx for idx in expected_indexes if idx not in existing_indexes]
        
        if missing_indexes:
            print("⚠️ Some indexes are missing (performance may be affected):")
            print(f"Missing indexes: {missing_indexes}")
        
        print("✅ Migration verification successful!")
        print("Enhanced report system tables are properly configured.")
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_enhanced_reports_functionality():
    """Test basic enhanced reports functionality"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nTesting enhanced reports functionality...")
        
        # Test inserting sample data (will be rolled back)
        cursor.execute("""
            INSERT INTO report_card_comment (student_id, teacher_id, comment_text)
            VALUES (999999, 999999, 'Test report card comment for migration verification')
        """)
        
        cursor.execute("""
            INSERT INTO quiz_feedback (attempt_id, teacher_id, comment_text)
            VALUES (999999, 999999, 'Test quiz feedback for migration verification')
        """)
        
        # Test querying the data
        cursor.execute("""
            SELECT id, comment_text FROM report_card_comment 
            WHERE comment_text = 'Test report card comment for migration verification'
        """)
        
        report_result = cursor.fetchone()
        if report_result:
            print("✓ Report card comment insert and query operations work correctly")
            
            cursor.execute("""
                SELECT id, comment_text FROM quiz_feedback 
                WHERE comment_text = 'Test quiz feedback for migration verification'
            """)
            
            feedback_result = cursor.fetchone()
            if feedback_result:
                print("✓ Quiz feedback insert and query operations work correctly")
                
                # Test updating data
                cursor.execute("""
                    UPDATE report_card_comment 
                    SET comment_text = 'Updated test comment'
                    WHERE id = ?
                """, (report_result[0],))
                
                cursor.execute("""
                    UPDATE quiz_feedback 
                    SET comment_text = 'Updated test feedback'
                    WHERE id = ?
                """, (feedback_result[0],))
                
                print("✓ Update operations work correctly")
                
                # Clean up test data
                cursor.execute("DELETE FROM report_card_comment WHERE id = ?", (report_result[0],))
                cursor.execute("DELETE FROM quiz_feedback WHERE id = ?", (feedback_result[0],))
                print("✓ Delete operations work correctly")
        
        conn.rollback()  # Don't actually save test data
        print("✅ All basic operations are functional!")
        
    except Exception as e:
        print(f"❌ Error during functionality test: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Enhanced Reports Migration")
    print("=" * 50)
    migrate_enhanced_reports()
    verify_migration()
    test_enhanced_reports_functionality()
    print("=" * 50)
    print("Migration completed!")
