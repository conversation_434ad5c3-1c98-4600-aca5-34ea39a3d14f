{% extends "base.html" %}

{% block title %}Respond to Parent Comment{% endblock %}

{% block content %}
<div class="container respond-container">
    <div class="respond-header">
        <h1>{% if comment.teacher_response %}View{% else %}Respond to{% endif %} Parent Comment</h1>
        <a href="{{ url_for('teacher_view_comments') }}" class="btn btn-secondary">Back to Comments</a>
    </div>

    <!-- Quiz and Student Information -->
    <div class="context-card">
        <h2>Quiz Context</h2>
        <div class="context-details">
            <div class="detail-row">
                <span class="label">Quiz:</span>
                <span class="value">{{ comment.quiz.title }}</span>
            </div>
            <div class="detail-row">
                <span class="label">Student:</span>
                <span class="value">{{ comment.student.name }}</span>
            </div>
            <div class="detail-row">
                <span class="label">Parent:</span>
                <span class="value">{{ comment.parent.name }} ({{ comment.parent.email }})</span>
            </div>
            <div class="detail-row">
                <span class="label">Score:</span>
                <span class="value">{{ "%.1f"|format(comment.attempt.score) }}%</span>
            </div>
            <div class="detail-row">
                <span class="label">Date Taken:</span>
                <span class="value">{{ comment.attempt.submitted_at.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
        </div>
    </div>

    <!-- Parent Comment -->
    <div class="comment-card">
        <div class="comment-header">
            <h3>Parent Comment</h3>
            <span class="comment-date">{{ comment.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
        </div>
        <div class="comment-text">{{ comment.comment_text }}</div>
    </div>

    <!-- Teacher Response Section -->
    <div class="response-card">
        {% if comment.teacher_response %}
        <!-- Existing Response -->
        <div class="existing-response">
            <h3>Your Response</h3>
            <div class="response-text">{{ comment.teacher_response }}</div>
            <div class="response-meta">
                <span>Responded on {{ comment.teacher_response_timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
        </div>
        
        <!-- Option to Update Response -->
        <div class="update-response">
            <h4>Update Your Response</h4>
            <p class="update-description">You can modify your response if needed. The parent will see the updated version.</p>
            
            <form method="post" class="response-form">
                <div class="form-group">
                    <label for="response_text">Updated Response:</label>
                    <textarea id="response_text" name="response_text" rows="4" maxlength="1000" 
                              placeholder="Enter your updated response to the parent..." required>{{ comment.teacher_response }}</textarea>
                    <div class="character-count">
                        <span id="char-count">{{ comment.teacher_response|length }}</span>/1000 characters
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Update Response</button>
            </form>
        </div>
        {% else %}
        <!-- New Response -->
        <div class="new-response">
            <h3>Your Response</h3>
            <p class="response-description">Respond to the parent's comment about their child's quiz performance. Your response will be visible to the parent when they view the quiz results.</p>
            
            <form method="post" class="response-form">
                <div class="form-group">
                    <label for="response_text">Response:</label>
                    <textarea id="response_text" name="response_text" rows="4" maxlength="1000" 
                              placeholder="Enter your response to the parent..." required></textarea>
                    <div class="character-count">
                        <span id="char-count">0</span>/1000 characters
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Send Response</button>
            </form>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3>Quick Actions</h3>
        <div class="actions-grid">
            <a href="{{ url_for('view_past_result', attempt_id=comment.attempt.id) }}" class="action-card">
                <div class="action-icon">📊</div>
                <div class="action-text">
                    <h4>View Full Results</h4>
                    <p>See detailed quiz results and answers</p>
                </div>
            </a>
            <a href="{{ url_for('view_quiz', quiz_id=comment.quiz.id) }}" class="action-card">
                <div class="action-icon">📝</div>
                <div class="action-text">
                    <h4>View Quiz Details</h4>
                    <p>Review quiz questions and settings</p>
                </div>
            </a>
            <a href="{{ url_for('parent_dashboard') }}" class="action-card">
                <div class="action-icon">👨‍👩‍👧‍👦</div>
                <div class="action-text">
                    <h4>Contact Parent</h4>
                    <p>Email: {{ comment.parent.email }}</p>
                </div>
            </a>
        </div>
    </div>
</div>

<script>
// Character counter for response textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('response_text');
    const charCount = document.getElementById('char-count');
    
    if (textarea && charCount) {
        // Set initial count
        charCount.textContent = textarea.value.length;
        
        textarea.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            if (count > 900) {
                charCount.style.color = '#dc3545';
            } else if (count > 800) {
                charCount.style.color = '#ffc107';
            } else {
                charCount.style.color = '#6c757d';
            }
        });
    }
});
</script>

<style>
.respond-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.respond-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.respond-header h1 {
    margin: 0;
    color: #343a40;
}

.context-card,
.comment-card,
.response-card,
.quick-actions {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.context-card h2,
.comment-card h3,
.response-card h3,
.quick-actions h3 {
    margin: 0 0 1rem 0;
    color: #007bff;
}

.context-details {
    display: grid;
    gap: 0.75rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.detail-row .label {
    font-weight: 600;
    color: #495057;
}

.detail-row .value {
    color: #6c757d;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.comment-header h3 {
    margin: 0;
    color: #495057;
}

.comment-date {
    color: #6c757d;
    font-size: 0.9rem;
}

.comment-text {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    line-height: 1.5;
    color: #495057;
}

.existing-response {
    margin-bottom: 2rem;
}

.response-text {
    background: #e8f5e8;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #28a745;
    line-height: 1.5;
    color: #495057;
    margin-bottom: 0.5rem;
}

.response-meta {
    font-size: 0.85rem;
    color: #6c757d;
    font-style: italic;
}

.update-response {
    border-top: 1px solid #e9ecef;
    padding-top: 1.5rem;
}

.update-response h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
}

.update-description,
.response-description {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.response-form .form-group {
    margin-bottom: 1rem;
}

.response-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.response-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
}

.response-form textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.character-count {
    text-align: right;
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.action-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
    text-decoration: none;
    color: inherit;
}

.action-icon {
    font-size: 1.5rem;
}

.action-text h4 {
    margin: 0 0 0.25rem 0;
    color: #495057;
    font-size: 0.9rem;
}

.action-text p {
    margin: 0;
    color: #6c757d;
    font-size: 0.8rem;
}

.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .respond-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .detail-row {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
