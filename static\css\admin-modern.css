/* Modern Admin Dashboard Design System */

/* Base Variables */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea, #764ba2);
    --success-gradient: linear-gradient(135deg, #28a745, #20c997);
    --warning-gradient: linear-gradient(135deg, #ffc107, #fd7e14);
    --danger-gradient: linear-gradient(135deg, #dc3545, #e83e8c);
    --info-gradient: linear-gradient(135deg, #17a2b8, #20c997);
    --secondary-gradient: linear-gradient(135deg, #6c757d, #495057);
    
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    --card-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --border-light: #e9ecef;
}

/* Modern Container */
.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Modern Header */
.admin-header {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.admin-header h1 {
    margin: 0;
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-header .subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-top: 0.5rem;
}

/* Modern Cards */
.admin-card, .data-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.admin-card:hover, .data-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--bg-light);
}

.card-header h2, .card-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

.btn-info {
    background: var(--info-gradient);
    color: white;
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--border-light);
    color: var(--text-secondary);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: white;
}

.btn-outline:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
}

/* Modern Tables */
.table-container {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.modern-table th {
    background: var(--bg-light);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 10;
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--bg-light);
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
    background: var(--bg-light);
}

/* Modern Badges */
.badge {
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.badge-primary { background: var(--primary-gradient); }
.badge-success { background: var(--success-gradient); }
.badge-warning { background: var(--warning-gradient); }
.badge-danger { background: var(--danger-gradient); }
.badge-info { background: var(--info-gradient); }
.badge-secondary { background: var(--secondary-gradient); }

/* Status Badges */
.status-badge {
    padding: 0.3rem 0.7rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: var(--success-gradient);
    color: white;
}

.status-inactive {
    background: var(--secondary-gradient);
    color: white;
}

.status-pending {
    background: var(--warning-gradient);
    color: white;
}

.status-locked {
    background: var(--danger-gradient);
    color: white;
}

/* Modern Grid */
.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Modern Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

.stat-card.primary { border-left-color: #667eea; }
.stat-card.success { border-left-color: #28a745; }
.stat-card.warning { border-left-color: #ffc107; }
.stat-card.danger { border-left-color: #dc3545; }
.stat-card.info { border-left-color: #17a2b8; }

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.stat-icon {
    float: right;
    font-size: 2rem;
    opacity: 0.3;
}

/* Modern Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-white);
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Modern Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(32, 201, 151, 0.1));
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        padding: 1rem;
    }
    
    .admin-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .admin-header h1 {
        font-size: 1.5rem;
    }
    
    .admin-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }
}
