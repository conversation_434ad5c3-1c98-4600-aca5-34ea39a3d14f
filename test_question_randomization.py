#!/usr/bin/env python3
"""
Test script for question randomization system.
This script validates that the question randomization system works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, QuizAttempt, Question, User, generate_randomized_question_order, get_questions_in_randomized_order, should_randomize_questions
from werkzeug.security import generate_password_hash
import json

def test_question_randomization():
    """Test the question randomization system"""
    with app.app_context():
        print("Testing Question Randomization System")
        print("=" * 50)
        
        # Create test teacher
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Randomization Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
            db.session.commit()
            print("✓ Created test teacher")
        
        # Create test students
        students = []
        for i in range(3):
            student = User.query.filter_by(email=f'test_randomization_student{i}@jpischool.com').first()
            if not student:
                student = User(
                    name=f'Test Randomization Student {i+1}',
                    email=f'test_randomization_student{i}@jpischool.com',
                    password=generate_password_hash('password123'),
                    unhashed_password='password123',
                    role='student',
                    is_verified=True
                )
                db.session.add(student)
                students.append(student)
        db.session.commit()
        print(f"✓ Created {len(students)} test students")
        
        # Test 1: Create quiz with randomization enabled
        print("\nTest 1: Creating quiz with randomization enabled...")
        randomized_quiz = Quiz(
            title='Test Quiz with Randomization',
            description='A test quiz to validate question randomization',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False,
            randomize_questions=True
        )
        db.session.add(randomized_quiz)
        db.session.flush()
        
        # Add test questions
        questions = []
        for i in range(5):
            question = Question(
                quiz_id=randomized_quiz.id,
                question_text=f'Question {i+1}: What is {i+1} + {i+1}?',
                question_type='mcq',
                option1=f'{i}',
                option2=f'{(i+1)*2}',
                option3=f'{i+3}',
                option4=f'{i+5}',
                correct_answer='2',
                marks=20
            )
            questions.append(question)
            db.session.add(question)
        
        db.session.commit()
        print(f"✓ Created randomized quiz with {len(questions)} questions")
        
        # Test 2: Create quiz without randomization
        print("\nTest 2: Creating quiz without randomization...")
        normal_quiz = Quiz(
            title='Test Quiz without Randomization',
            description='A test quiz without question randomization',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False,
            randomize_questions=False
        )
        db.session.add(normal_quiz)
        db.session.flush()
        
        # Add test questions
        for i in range(5):
            question = Question(
                quiz_id=normal_quiz.id,
                question_text=f'Normal Question {i+1}: What is {i+1} * 2?',
                question_type='mcq',
                option1=f'{i}',
                option2=f'{(i+1)*2}',
                option3=f'{i+3}',
                option4=f'{i+5}',
                correct_answer='2',
                marks=20
            )
            db.session.add(question)
        
        db.session.commit()
        print("✓ Created normal quiz without randomization")
        
        # Test 3: Test randomization detection
        print("\nTest 3: Testing randomization detection...")
        assert should_randomize_questions(randomized_quiz), "Randomized quiz should be detected"
        assert not should_randomize_questions(normal_quiz), "Normal quiz should not be detected as randomized"
        print("✓ Randomization detection works correctly")
        
        # Test 4: Test question order generation
        print("\nTest 4: Testing question order generation...")
        
        # Generate orders for different students
        orders = {}
        for i, student in enumerate(students):
            order = generate_randomized_question_order(randomized_quiz.id, student.id)
            orders[student.id] = order
            print(f"  Student {i+1} order: {order}")
        
        # Verify orders are different (with high probability)
        order_values = list(orders.values())
        unique_orders = len(set(order_values))
        print(f"✓ Generated {unique_orders} unique orders out of {len(students)} students")
        
        # Test 5: Test consistent order generation
        print("\nTest 5: Testing consistent order generation...")
        student = students[0]
        order1 = generate_randomized_question_order(randomized_quiz.id, student.id)
        order2 = generate_randomized_question_order(randomized_quiz.id, student.id)
        
        assert order1 == order2, "Same student should get same order for same quiz"
        print("✓ Order generation is consistent for same student-quiz combination")
        
        # Test 6: Test question retrieval in randomized order
        print("\nTest 6: Testing question retrieval in randomized order...")
        
        student = students[0]
        randomized_order = generate_randomized_question_order(randomized_quiz.id, student.id)
        
        # Get questions in randomized order
        randomized_questions = get_questions_in_randomized_order(randomized_quiz.id, randomized_order)
        original_questions = Question.query.filter_by(quiz_id=randomized_quiz.id).all()
        
        # Verify same questions, potentially different order
        assert len(randomized_questions) == len(original_questions), "Should have same number of questions"
        
        randomized_ids = [q.id for q in randomized_questions]
        original_ids = [q.id for q in original_questions]
        
        assert set(randomized_ids) == set(original_ids), "Should have same question IDs"
        print("✓ Question retrieval in randomized order works correctly")
        
        # Test 7: Test quiz attempt with randomization
        print("\nTest 7: Testing quiz attempt with randomization...")
        
        student = students[0]
        randomized_order = generate_randomized_question_order(randomized_quiz.id, student.id)
        
        # Create quiz attempt with randomization
        attempt = QuizAttempt(
            student_id=student.id,
            quiz_id=randomized_quiz.id,
            score=85.0,
            quiz_version=randomized_quiz.version_number,
            is_locked=False,
            randomized_question_order=randomized_order
        )
        db.session.add(attempt)
        db.session.commit()
        
        print(f"✓ Created quiz attempt with randomized order: {randomized_order}")
        
        # Test 8: Test result viewing with randomized order
        print("\nTest 8: Testing result viewing with randomized order...")
        
        # Get questions in the order they were presented
        result_questions = get_questions_in_randomized_order(attempt.quiz_id, attempt.randomized_question_order)
        
        # Verify the order matches what was stored
        stored_order = json.loads(attempt.randomized_question_order)
        result_order = [q.id for q in result_questions]
        
        assert result_order == stored_order, "Result viewing should show questions in same order as presented"
        print("✓ Result viewing preserves question order correctly")
        
        # Test 9: Test fallback for invalid JSON
        print("\nTest 9: Testing fallback for invalid randomization data...")
        
        # Test with invalid JSON
        fallback_questions = get_questions_in_randomized_order(randomized_quiz.id, "invalid_json")
        original_questions = Question.query.filter_by(quiz_id=randomized_quiz.id).all()
        
        assert len(fallback_questions) == len(original_questions), "Should fallback to original order"
        print("✓ Fallback for invalid JSON works correctly")
        
        # Test 10: Test normal quiz (no randomization)
        print("\nTest 10: Testing normal quiz without randomization...")
        
        normal_questions = get_questions_in_randomized_order(normal_quiz.id, None)
        original_normal_questions = Question.query.filter_by(quiz_id=normal_quiz.id).all()
        
        normal_ids = [q.id for q in normal_questions]
        original_normal_ids = [q.id for q in original_normal_questions]
        
        assert normal_ids == original_normal_ids, "Normal quiz should maintain original order"
        print("✓ Normal quiz maintains original question order")
        
        # Cleanup
        print("\nCleaning up test data...")
        try:
            # Delete attempts first (foreign key constraint)
            QuizAttempt.query.filter_by(quiz_id=randomized_quiz.id).delete()
            QuizAttempt.query.filter_by(quiz_id=normal_quiz.id).delete()
            
            # Delete questions
            Question.query.filter_by(quiz_id=randomized_quiz.id).delete()
            Question.query.filter_by(quiz_id=normal_quiz.id).delete()
            
            # Delete quizzes
            db.session.delete(randomized_quiz)
            db.session.delete(normal_quiz)
            
            # Delete test users
            for student in students:
                db.session.delete(student)
            db.session.delete(teacher)
            
            db.session.commit()
            print("✓ Test data cleaned up successfully")
        except Exception as e:
            print(f"Warning: Could not clean up all test data: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All question randomization tests passed!")
        print("\nSummary:")
        print("- Question randomization detection works")
        print("- Random order generation is consistent per student")
        print("- Different students get different orders")
        print("- Question retrieval respects randomized order")
        print("- Quiz attempts store randomization data")
        print("- Result viewing preserves question order")
        print("- Fallback mechanisms work for edge cases")
        print("- Normal quizzes maintain original order")
        
        return True

if __name__ == "__main__":
    try:
        success = test_question_randomization()
        if success:
            print("\n✅ Question randomization system test completed successfully!")
        else:
            print("\n❌ Question randomization system test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
