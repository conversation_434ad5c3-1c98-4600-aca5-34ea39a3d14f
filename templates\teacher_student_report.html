{% extends "base.html" %}

{% block title %}Student Report - {{ student.name }}{% endblock %}

{% block content %}
<div class="container teacher-report-container">
    <div class="report-header">
        <h1>Student Performance Report</h1>
        <h2>{{ student.name }}</h2>
        <div class="header-actions">
            <a href="{{ url_for('export_report_card', student_id=student.id) }}" class="btn btn-danger">
                <i class="fas fa-file-pdf"></i> Export Report Card PDF
            </a>
            <a href="{{ url_for('manage_student_reports') }}" class="btn btn-secondary">Back to Reports</a>
        </div>
    </div>

    <!-- Student Overview -->
    <div class="report-section overview-section">
        <h3>Student Overview</h3>
        <div class="overview-grid">
            <div class="overview-item">
                <span class="label">Student Email:</span>
                <span class="value">{{ student.email }}</span>
            </div>
            <div class="overview-item">
                <span class="label">Parent:</span>
                <span class="value">
                    {% if parent %}
                        {{ parent.name }} ({{ parent.email }})
                    {% else %}
                        No parent linked
                    {% endif %}
                </span>
            </div>
            <div class="overview-item">
                <span class="label">Total Attempts:</span>
                <span class="value">{{ performance_data.total_attempts }}</span>
            </div>
            <div class="overview-item">
                <span class="label">Average Score:</span>
                <span class="value">{{ "%.1f"|format(performance_data.average_score) }}%</span>
            </div>
        </div>
    </div>

    <!-- Performance Summary -->
    <div class="report-section performance-section">
        <h3>Performance Summary</h3>
        <div class="performance-grid">
            <div class="performance-chart">
                <h4>Score Trend</h4>
                <canvas id="trendChart"></canvas>
            </div>
            <div class="grade-distribution">
                <h4>Grade Distribution</h4>
                <div class="grade-bars">
                    {% for grade, count in performance_data.grade_distribution.items() %}
                    <div class="grade-bar">
                        <span class="grade-label">{{ grade }}</span>
                        <div class="bar-container">
                            <div class="bar grade-{{ grade|lower }}" style="width: {{ (count / performance_data.total_attempts * 100) if performance_data.total_attempts > 0 else 0 }}%"></div>
                        </div>
                        <span class="grade-count">{{ count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Report Card Comment -->
    <div class="report-section comment-section">
        <h3>Overall Report Card Comment</h3>
        {% if report_comment %}
        <div class="existing-comment">
            <div class="comment-header">
                <span class="comment-author">Your Comment</span>
                <span class="comment-date">{{ report_comment.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
            <div class="comment-text">{{ report_comment.comment_text }}</div>
        </div>
        {% endif %}
        
        <form method="post" action="{{ url_for('add_report_card_comment', student_id=student.id) }}" class="comment-form">
            <div class="form-group">
                <label for="comment_text">{% if report_comment %}Update{% else %}Add{% endif %} Overall Comment:</label>
                <textarea id="comment_text" name="comment_text" rows="4" maxlength="2000" 
                          placeholder="Enter your overall assessment of {{ student.name }}'s performance..." required>{% if report_comment %}{{ report_comment.comment_text }}{% endif %}</textarea>
                <div class="character-count">
                    <span id="char-count">{% if report_comment %}{{ report_comment.comment_text|length }}{% else %}0{% endif %}</span>/2000 characters
                </div>
            </div>
            <button type="submit" class="btn btn-primary">{% if report_comment %}Update{% else %}Save{% endif %} Comment</button>
        </form>
    </div>

    <!-- Quiz Attempts with Feedback -->
    <div class="report-section attempts-section">
        <h3>Quiz Attempts & Feedback</h3>
        {% if attempts_with_feedback %}
            {% for item in attempts_with_feedback %}
            <div class="attempt-card">
                <div class="attempt-header">
                    <div class="attempt-info">
                        <h4>{{ item.attempt.quiz.title }}</h4>
                        <div class="attempt-meta">
                            <span class="score">Score: {{ "%.1f"|format(item.attempt.score) }}%</span>
                            <span class="grade grade-{{ item.grade|lower }}">{{ item.grade }}</span>
                            <span class="date">{{ item.attempt.submitted_at.strftime('%B %d, %Y') }}</span>
                        </div>
                    </div>
                    <div class="attempt-actions">
                        <a href="{{ url_for('view_past_result', attempt_id=item.attempt.id) }}" class="btn btn-sm btn-info">View Details</a>
                    </div>
                </div>

                <!-- Parent Comments for this attempt -->
                {% if item.parent_comments %}
                <div class="parent-comments">
                    <h5>Parent Comments:</h5>
                    {% for comment in item.parent_comments %}
                    <div class="parent-comment">
                        <div class="comment-header">
                            <span class="comment-author">{{ comment.parent.name }}</span>
                            <span class="comment-date">{{ comment.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                        </div>
                        <div class="comment-text">{{ comment.comment_text }}</div>
                        {% if comment.teacher_response %}
                        <div class="teacher-response">
                            <div class="response-header">
                                <span class="response-label">Your Response:</span>
                                <span class="response-date">{{ comment.teacher_response_timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                            </div>
                            <div class="response-text">{{ comment.teacher_response }}</div>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Teacher Quiz Feedback -->
                <div class="quiz-feedback">
                    <h5>Your Quiz Feedback:</h5>
                    {% if item.quiz_feedback %}
                    <div class="existing-feedback">
                        <div class="feedback-header">
                            <span class="feedback-date">{{ item.quiz_feedback.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                        </div>
                        <div class="feedback-text">{{ item.quiz_feedback.comment_text }}</div>
                    </div>
                    {% endif %}
                    
                    <form method="post" action="{{ url_for('add_quiz_feedback', attempt_id=item.attempt.id) }}" class="feedback-form">
                        <div class="form-group">
                            <textarea name="comment_text" rows="3" maxlength="1000" 
                                      placeholder="Add specific feedback for this quiz attempt...">{% if item.quiz_feedback %}{{ item.quiz_feedback.comment_text }}{% endif %}</textarea>
                            <div class="character-count">
                                <span class="quiz-char-count">{% if item.quiz_feedback %}{{ item.quiz_feedback.comment_text|length }}{% else %}0{% endif %}</span>/1000 characters
                            </div>
                        </div>
                        <button type="submit" class="btn btn-sm btn-primary">{% if item.quiz_feedback %}Update{% else %}Add{% endif %} Feedback</button>
                    </form>
                </div>
            </div>
            {% endfor %}
        {% else %}
        <div class="no-attempts">
            <p>No quiz attempts found for this student.</p>
        </div>
        {% endif %}
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for overall comment
    const textarea = document.getElementById('comment_text');
    const charCount = document.getElementById('char-count');
    
    if (textarea && charCount) {
        textarea.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            if (count > 1800) {
                charCount.style.color = '#dc3545';
            } else if (count > 1600) {
                charCount.style.color = '#ffc107';
            } else {
                charCount.style.color = '#6c757d';
            }
        });
    }

    // Character counters for quiz feedback forms
    const feedbackTextareas = document.querySelectorAll('.feedback-form textarea');
    feedbackTextareas.forEach((textarea, index) => {
        const charCount = textarea.parentElement.querySelector('.quiz-char-count');
        if (charCount) {
            textarea.addEventListener('input', function() {
                const count = this.value.length;
                charCount.textContent = count;
                
                if (count > 900) {
                    charCount.style.color = '#dc3545';
                } else if (count > 800) {
                    charCount.style.color = '#ffc107';
                } else {
                    charCount.style.color = '#6c757d';
                }
            });
        }
    });

    // Performance trend chart
    const ctx = document.getElementById('trendChart');
    if (ctx) {
        const trendData = {{ performance_data.trend_data | tojson }};
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.labels,
                datasets: [{
                    label: 'Quiz Scores (%)',
                    data: trendData.scores,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Score (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Quiz Attempts'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
});
</script>

<style>
.teacher-report-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.report-header h1 {
    margin: 0;
    color: #343a40;
}

.report-header h2 {
    margin: 0;
    color: #007bff;
    font-weight: normal;
}

.report-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-section h3 {
    margin: 0 0 1.5rem 0;
    color: #007bff;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

/* Overview Section */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.overview-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.overview-item .label {
    font-weight: 600;
    color: #495057;
}

.overview-item .value {
    color: #6c757d;
}

/* Performance Section */
.performance-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.performance-chart h4,
.grade-distribution h4 {
    margin: 0 0 1rem 0;
    color: #495057;
}

.grade-bars {
    display: grid;
    gap: 0.75rem;
}

.grade-bar {
    display: grid;
    grid-template-columns: 30px 1fr 30px;
    align-items: center;
    gap: 0.5rem;
}

.grade-label {
    font-weight: bold;
    text-align: center;
}

.bar-container {
    background: #e9ecef;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.bar.grade-a { background: #28a745; }
.bar.grade-b { background: #007bff; }
.bar.grade-c { background: #ffc107; }
.bar.grade-d { background: #fd7e14; }
.bar.grade-f { background: #dc3545; }

.grade-count {
    text-align: center;
    font-weight: bold;
}

/* Comment Section */
.existing-comment {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #007bff;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-author {
    font-weight: 600;
    color: #495057;
}

.comment-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.comment-text {
    color: #495057;
    line-height: 1.5;
}

.comment-form .form-group {
    margin-bottom: 1rem;
}

.comment-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.comment-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: inherit;
    resize: vertical;
}

.comment-form textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.character-count {
    text-align: right;
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Attempts Section */
.attempt-card {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background: #f8f9fa;
}

.attempt-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.attempt-info h4 {
    margin: 0 0 0.5rem 0;
    color: #007bff;
}

.attempt-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
}

.attempt-meta .score {
    font-weight: 600;
    color: #495057;
}

.attempt-meta .grade {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: bold;
    font-size: 0.8rem;
}

.grade.grade-a { background: #d4edda; color: #155724; }
.grade.grade-b { background: #cce7ff; color: #004085; }
.grade.grade-c { background: #fff3cd; color: #856404; }
.grade.grade-d { background: #f8d7da; color: #721c24; }
.grade.grade-f { background: #f8d7da; color: #721c24; }

.attempt-meta .date {
    color: #6c757d;
}

.parent-comments,
.quiz-feedback {
    margin-top: 1rem;
}

.parent-comments h5,
.quiz-feedback h5 {
    margin: 0 0 0.75rem 0;
    color: #495057;
    font-size: 1rem;
}

.parent-comment {
    background: white;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border-left: 4px solid #17a2b8;
}

.teacher-response {
    background: #e8f5e8;
    border-radius: 4px;
    padding: 0.75rem;
    margin-top: 0.75rem;
    border-left: 4px solid #28a745;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.response-label {
    font-weight: 600;
    color: #28a745;
    font-size: 0.9rem;
}

.response-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.response-text {
    color: #495057;
    line-height: 1.5;
}

.existing-feedback {
    background: white;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
}

.feedback-header {
    margin-bottom: 0.5rem;
}

.feedback-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.feedback-text {
    color: #495057;
    line-height: 1.5;
}

.feedback-form .form-group {
    margin-bottom: 0.75rem;
}

.feedback-form textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: inherit;
    resize: vertical;
}

.no-attempts {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
    color: white;
    text-decoration: none;
}

.btn-danger i {
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .report-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .performance-grid {
        grid-template-columns: 1fr;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .attempt-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .attempt-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .comment-header,
    .response-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>
{% endblock %}
