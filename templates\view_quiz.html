{% extends "base.html" %}

{% block title %}View Quiz: {{ quiz.title }}{% endblock %}

{% block content %}
<div class="container view-quiz-container">
    <div class="quiz-header">
        <h1>{{ quiz.title }}</h1>
        <div class="header-actions">
            {% if quiz.original_quiz_id or quiz.versions %}
                <a href="{{ url_for('quiz_versions', quiz_id=quiz.id) }}" class="btn btn-info">Version History</a>
            {% endif %}
            <a href="{{ url_for('my_quizzes') }}" class="btn btn-secondary">Back to My Quizzes</a>
        </div>
    </div>

    <div class="quiz-meta">
        <span class="meta-item"><strong>Difficulty:</strong> <span class="difficulty-badge {{ quiz.difficulty }}">{{ quiz.difficulty }}</span></span>
        <span class="meta-item"><strong>Time Limit:</strong> {{ quiz.time_limit }} minutes</span>
        <span class="meta-item"><strong>Total Marks:</strong> {{ quiz.total_marks }}</span>
        <span class="meta-item"><strong>Version:</strong> v{{ quiz.version_number }}{% if quiz.original_quiz_id %} (Revision){% endif %}</span>
        <span class="meta-item">
            <strong>Status:</strong>
            {% if quiz.is_locked %}
                <span class="status-badge status-locked">Locked</span>
            {% elif quiz.is_active %}
                <span class="status-badge status-active">Active</span>
            {% else %}
                <span class="status-badge status-inactive">Inactive</span>
            {% endif %}
        </span>
    </div>

    <!-- Quiz Statistics -->
    <div class="quiz-stats">
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">{{ questions|length }}</span>
                <span class="stat-label">Questions</span>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ total_attempts }}</span>
                <span class="stat-label">Attempts</span>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ "%.1f"|format(avg_score) }}%</span>
                <span class="stat-label">Avg Score</span>
            </div>
        </div>
    </div>
    {% if quiz.description %}
        <div class="quiz-description-view">
            <strong>Description:</strong>
            <p>{{ quiz.description }}</p>
        </div>
    {% endif %}

    <!-- Version History Section -->
    {% if quiz.original_quiz_id or quiz.versions %}
    <div class="version-history-section">
        <h2>Version History</h2>
        <div class="version-list">
            <!-- Show original quiz if this is a version -->
            {% if quiz.original_quiz_id %}
                {% set original = quiz.original_quiz %}
                <div class="version-item {% if original.is_locked %}locked{% endif %}">
                    <div class="version-header">
                        <span class="version-number">v{{ original.version_number }}</span>
                        <span class="version-title">{{ original.title }}</span>
                        {% if original.is_locked %}
                            <span class="lock-icon">🔒</span>
                        {% endif %}
                        {% if original.id == quiz.id %}
                            <span class="current-badge">Current</span>
                        {% endif %}
                    </div>
                    <div class="version-details">
                        <span>Created: {{ original.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        {% if original.locked_at %}
                            <span>Locked: {{ original.locked_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        {% endif %}
                        <span>Attempts: {{ original.attempts|length }}</span>
                    </div>
                    {% if original.id != quiz.id %}
                        <div class="version-actions">
                            <a href="{{ url_for('view_quiz', quiz_id=original.id) }}" class="btn btn-sm">View</a>
                        </div>
                    {% endif %}
                </div>
            {% endif %}

            <!-- Show all versions -->
            {% for version in quiz.versions %}
                <div class="version-item {% if version.is_locked %}locked{% endif %}">
                    <div class="version-header">
                        <span class="version-number">v{{ version.version_number }}</span>
                        <span class="version-title">{{ version.title }}</span>
                        {% if version.is_locked %}
                            <span class="lock-icon">🔒</span>
                        {% endif %}
                        {% if version.id == quiz.id %}
                            <span class="current-badge">Current</span>
                        {% endif %}
                    </div>
                    <div class="version-details">
                        <span>Created: {{ version.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        {% if version.locked_at %}
                            <span>Locked: {{ version.locked_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        {% endif %}
                        <span>Attempts: {{ version.attempts|length }}</span>
                    </div>
                    {% if version.id != quiz.id %}
                        <div class="version-actions">
                            <a href="{{ url_for('view_quiz', quiz_id=version.id) }}" class="btn btn-sm">View</a>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="questions-view-section">
        <h2>Questions</h2>
        {% for question in questions %}
            <div class="question-view-card">
                <div class="question-view-header">
                    <span class="question-view-number">Question {{ loop.index }}</span>
                    <span class="question-view-marks">({{ question.marks }} Marks)</span>
                </div>
                <p class="question-view-text">{{ question.question_text }}</p>
                
                <!-- Assuming MCQ type -->
                <div class="options-view">
                    <p><strong>Options:</strong></p>
                    <ul>
                        <li {% if question.correct_answer == '1' %}class="correct-answer"{% endif %}>A. {{ question.option1 }}</li>
                        <li {% if question.correct_answer == '2' %}class="correct-answer"{% endif %}>B. {{ question.option2 }}</li>
                        {% if question.option3 %}
                            <li {% if question.correct_answer == '3' %}class="correct-answer"{% endif %}>C. {{ question.option3 }}</li>
                        {% endif %}
                        {% if question.option4 %}
                            <li {% if question.correct_answer == '4' %}class="correct-answer"{% endif %}>D. {{ question.option4 }}</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        {% else %}
            <p>No questions found for this quiz.</p>
        {% endfor %}
    </div>

    <!-- Version Information -->
    {% if quiz.original_quiz_id or quiz.versions %}
    <div class="versions-section">
        <h2>Quiz Versions</h2>
        <div class="versions-list">
            {% if quiz.original_quiz_id %}
                <div class="version-item">
                    <span class="version-info">v1 (Original) -
                        {% if quiz.original_quiz.is_locked %}Locked{% else %}Active{% endif %}
                    </span>
                    <a href="{{ url_for('view_quiz', quiz_id=quiz.original_quiz_id) }}" class="btn btn-sm">View Original</a>
                </div>
            {% endif %}

            {% if quiz.versions %}
                {% for version in quiz.versions %}
                <div class="version-item {% if version.id == quiz.id %}current-version{% endif %}">
                    <span class="version-info">
                        v{{ version.version_number }}
                        {% if version.id == quiz.id %} (Current){% endif %} -
                        {% if version.is_locked %}Locked{% elif version.is_active %}Active{% else %}Inactive{% endif %}
                    </span>
                    {% if version.id != quiz.id %}
                        <a href="{{ url_for('view_quiz', quiz_id=version.id) }}" class="btn btn-sm">View</a>
                    {% endif %}
                </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Recent Attempts -->
    {% if recent_attempts %}
    <div class="recent-attempts-section">
        <h2>Recent Attempts</h2>
        <div class="attempts-table">
            <table>
                <thead>
                    <tr>
                        <th>Student</th>
                        <th>Score</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attempt in recent_attempts %}
                    <tr>
                        <td>{{ attempt.student.name }}</td>
                        <td>{{ "%.1f"|format(attempt.score) }}%</td>
                        <td>{{ attempt.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- Warning for Locked Quiz -->
    {% if quiz.is_locked %}
    <div class="warning-section">
        <div class="warning-card">
            <h3>⚠️ Quiz Locked</h3>
            <p>This quiz has been locked because it has student attempts. The quiz cannot be edited or deleted to preserve the integrity of existing results.</p>
            {% if quiz.locked_at %}
                <p><small>Locked on: {{ quiz.locked_at.strftime('%Y-%m-%d %H:%M') }}</small></p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<style>
.view-quiz-container {
    max-width: 900px;
    margin: 2rem auto;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.quiz-header h1 {
    margin: 0;
    color: #343a40;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-info {
    background: #17a2b8;
    color: white;
    padding: 0.5rem 1rem;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.btn-info:hover {
    background: #138496;
}

.quiz-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    font-size: 0.95rem;
}

.meta-item strong {
    color: #495057;
}

.difficulty-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 10px;
    font-weight: bold;
    font-size: 0.85rem;
    text-transform: capitalize;
}
.difficulty-badge.easy { background-color: #d4edda; color: #155724; }
.difficulty-badge.medium { background-color: #fff3cd; color: #856404; }
.difficulty-badge.hard { background-color: #f8d7da; color: #721c24; }

.quiz-description-view {
    margin-bottom: 2rem;
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.quiz-description-view p {
    margin-top: 0.5rem;
    color: #6c757d;
    line-height: 1.6;
}

.questions-view-section h2 {
    margin-bottom: 1.5rem;
    color: #007bff;
}

.question-view-card {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-left: 4px solid #17a2b8; /* Different accent color */
    border-radius: 8px;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
}

.question-view-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 0.8rem;
}

.question-view-number {
    font-weight: bold;
    font-size: 1.1em;
    color: #17a2b8;
}

.question-view-marks {
    font-size: 0.9em;
    color: #6c757d;
}

.question-view-text {
    margin-bottom: 1.2rem;
    font-size: 1.1em;
    color: #212529;
}

.options-view ul {
    list-style: none;
    padding-left: 1.5rem;
    margin-top: 0.5rem;
}

.options-view li {
    padding: 0.3rem 0;
    color: #495057;
}

.options-view li.correct-answer {
    font-weight: bold;
    color: #28a745; /* Green for correct answer */
}
.options-view li.correct-answer::before {
    content: "✓ "; /* Checkmark for correct answer */
    color: #28a745;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}
.btn-secondary:hover {
    background-color: #5a6268;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active { background: #d4edda; color: #155724; }
.status-locked { background: #f8d7da; color: #721c24; }
.status-inactive { background: #e2e3e5; color: #6c757d; }

.quiz-stats {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.versions-section, .recent-attempts-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.versions-section h2, .recent-attempts-section h2 {
    margin-bottom: 1rem;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.versions-list {
    display: grid;
    gap: 0.5rem;
}

.version-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
}

.version-item.current-version {
    border-color: #007bff;
    background: #e7f3ff;
}

/* Version History Styles */
.version-history-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.version-history-section h2 {
    margin-bottom: 1rem;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.version-list {
    display: grid;
    gap: 0.75rem;
}

.version-item {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    transition: all 0.2s ease;
}

.version-item:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.version-item.locked {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.version-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0.5rem;
}

.version-number {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.9em;
}

.version-title {
    font-weight: 600;
    color: #495057;
    flex-grow: 1;
}

.lock-icon {
    color: #dc3545;
    font-size: 1.1em;
}

.current-badge {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: bold;
}

.version-details {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.version-actions {
    display: flex;
    gap: 5px;
}

.version-info {
    font-weight: 500;
    color: #495057;
}

.attempts-table table {
    width: 100%;
    border-collapse: collapse;
}

.attempts-table th,
.attempts-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.attempts-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.attempts-table tbody tr:hover {
    background: #f8f9fa;
}

.warning-section {
    margin-bottom: 2rem;
}

.warning-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid #f39c12;
}

.warning-card h3 {
    color: #856404;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.warning-card p {
    color: #856404;
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .version-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
</style>

{% endblock %} 