# Flask Quiz Management System - Complete Dependencies
# Successfully tested and installed on Windows with Python 3.13

# Core Flask Framework
Flask==2.3.3
Werkzeug==2.3.7
Jinja2==3.1.6
MarkupSafe==3.0.2
click==8.2.1
itsdangerous==2.2.0
blinker==1.9.0
colorama==0.4.6

# Database
Flask-SQLAlchemy==3.1.1
SQLAlchemy==2.0.41
greenlet==3.2.3
typing-extensions==4.14.1

# Email Support
Flask-Mail==0.9.1

# PDF Generation for Quiz and Report Card Export
xhtml2pdf==0.2.17
reportlab==4.4.2
html5lib==1.1
pypdf==5.7.0
Pillow==11.3.0
arabic-reshaper==3.0.0
python-bidi==0.6.6
svglib==1.5.1
tinycss2==1.4.0
cssselect2==0.8.0
webencodings==0.5.1

# PDF Dependencies
pyHanko==0.29.1
pyhanko-certvalidator==0.27.0
asn1crypto==1.5.1
oscrypto==1.3.0
uritools==5.0.0

# Cryptography and Security
cryptography==45.0.5
cffi==1.17.1
pycparser==2.22

# XML Processing
lxml==6.0.0

# Date and Time Utilities
python-dateutil==2.8.2
tzlocal==5.3.1
tzdata==2025.2

# HTTP Requests
requests==2.31.0
urllib3==2.5.0
certifi==2025.6.15
charset-normalizer==3.4.2
idna==3.10

# Environment Variables Management
python-dotenv==1.1.1

# Form Handling
Flask-WTF==1.2.2
WTForms==3.2.1

# YAML Processing
PyYAML==6.0.2

# Utilities
six==1.17.0

# Optional packages (uncomment if needed):
# mysql-connector-python==8.1.0  # For MySQL database
# pytest==7.4.2                  # For testing
# pytest-flask==1.2.0           # For Flask testing
# Flask-CORS==4.0.0              # For CORS support
# Flask-Limiter==3.5.0           # For rate limiting
# Flask-Caching==2.1.0           # For caching
# Flask-Session==0.5.0           # For session management
# gunicorn==21.2.0               # For production deployment
# PyJWT==2.8.0                   # For JWT authentication
# pytz==2023.3                   # For timezone handling
