#!/usr/bin/env python3
"""
Test script for user approval/rejection system.
This script validates that the user approval and rejection system works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, User
from werkzeug.security import generate_password_hash, check_password_hash
import uuid

def test_user_approval_system():
    """Test the user approval and rejection system"""
    with app.app_context():
        print("Testing User Approval/Rejection System")
        print("=" * 50)
        
        # Test 1: Create a test user for approval
        print("\nTest 1: Creating test user for approval...")
        test_email = f"test_user_{uuid.uuid4().hex[:8]}@jpischool.com"
        test_token = f"token_{uuid.uuid4().hex}"
        
        test_user = User(
            name='Test User for Approval',
            email=test_email,
            password=generate_password_hash('password123'),
            unhashed_password='password123',
            role='student',
            is_verified=False,
            verification_token=test_token
        )
        
        try:
            db.session.add(test_user)
            db.session.commit()
            print(f"✓ Created test user (ID: {test_user.id}, Email: {test_email})")
        except Exception as e:
            print(f"❌ Error creating test user: {e}")
            return False
        
        # Test 2: Verify user is not verified initially
        print("\nTest 2: Checking initial verification status...")
        assert not test_user.is_verified, "User should not be verified initially"
        assert test_user.verification_token == test_token, "Verification token should be set"
        print("✓ User is correctly unverified with token")
        
        # Test 3: Test login with unverified user
        print("\nTest 3: Testing login with unverified user...")
        user_check = User.query.filter_by(email=test_email).first()
        assert user_check is not None, "User should exist in database"
        assert check_password_hash(user_check.password, 'password123'), "Password should be correct"
        assert not user_check.is_verified, "User should not be verified"
        print("✓ Unverified user login validation works")
        
        # Test 4: Test user approval
        print("\nTest 4: Testing user approval...")
        try:
            # Simulate approval
            test_user.is_verified = True
            test_user.verification_token = None
            db.session.commit()
            
            # Verify approval
            db.session.refresh(test_user)
            assert test_user.is_verified, "User should be verified after approval"
            assert test_user.verification_token is None, "Token should be cleared after approval"
            print("✓ User approval works correctly")
        except Exception as e:
            print(f"❌ Error during user approval: {e}")
            return False
        
        # Test 5: Test login with verified user
        print("\nTest 5: Testing login with verified user...")
        verified_user = User.query.filter_by(email=test_email).first()
        assert verified_user.is_verified, "User should be verified"
        assert check_password_hash(verified_user.password, 'password123'), "Password should still work"
        print("✓ Verified user login validation works")
        
        # Test 6: Create another user for rejection test
        print("\nTest 6: Creating test user for rejection...")
        reject_email = f"reject_user_{uuid.uuid4().hex[:8]}@jpischool.com"
        reject_token = f"token_{uuid.uuid4().hex}"
        
        reject_user = User(
            name='Test User for Rejection',
            email=reject_email,
            password=generate_password_hash('password123'),
            unhashed_password='password123',
            role='teacher',
            is_verified=False,
            verification_token=reject_token
        )
        
        try:
            db.session.add(reject_user)
            db.session.commit()
            reject_user_id = reject_user.id
            print(f"✓ Created test user for rejection (ID: {reject_user_id})")
        except Exception as e:
            print(f"❌ Error creating rejection test user: {e}")
            return False
        
        # Test 7: Test user rejection (deletion)
        print("\nTest 7: Testing user rejection...")
        try:
            # Simulate rejection
            db.session.delete(reject_user)
            db.session.commit()
            
            # Verify deletion
            deleted_user = User.query.get(reject_user_id)
            assert deleted_user is None, "User should be deleted after rejection"
            print("✓ User rejection (deletion) works correctly")
        except Exception as e:
            print(f"❌ Error during user rejection: {e}")
            return False
        
        # Test 8: Test pending users query
        print("\nTest 8: Testing pending users query...")
        try:
            # Create another unverified user
            pending_email = f"pending_user_{uuid.uuid4().hex[:8]}@jpischool.com"
            pending_user = User(
                name='Pending User',
                email=pending_email,
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='parent',
                is_verified=False,
                verification_token=f"token_{uuid.uuid4().hex}"
            )
            db.session.add(pending_user)
            db.session.commit()
            
            # Query pending users
            pending_users = User.query.filter_by(is_verified=False).all()
            assert len(pending_users) >= 1, "Should have at least one pending user"
            
            pending_emails = [u.email for u in pending_users]
            assert pending_email in pending_emails, "New pending user should be in query results"
            print(f"✓ Pending users query works (found {len(pending_users)} pending users)")
            
            # Clean up
            db.session.delete(pending_user)
            db.session.commit()
            
        except Exception as e:
            print(f"❌ Error testing pending users query: {e}")
            return False
        
        # Clean up approved user
        try:
            db.session.delete(test_user)
            db.session.commit()
            print("✓ Cleaned up test users")
        except Exception as e:
            print(f"Warning: Could not clean up test user: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All user approval/rejection tests passed!")
        print("\nSummary:")
        print("- User creation works correctly")
        print("- Verification status tracking works")
        print("- User approval process works")
        print("- User rejection (deletion) works")
        print("- Pending users query works")
        print("- Login validation respects verification status")
        
        return True

def test_database_connection():
    """Test basic database connectivity"""
    with app.app_context():
        try:
            # Test basic query
            admin_count = User.query.filter_by(role='admin').count()
            print(f"✓ Database connection works (found {admin_count} admin users)")
            return True
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            return False

if __name__ == "__main__":
    try:
        print("Testing Database Connection...")
        if not test_database_connection():
            print("❌ Database connection failed!")
            sys.exit(1)
        
        print("\nTesting User Approval System...")
        success = test_user_approval_system()
        if success:
            print("\n✅ User approval/rejection system test completed successfully!")
        else:
            print("\n❌ User approval/rejection system test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
