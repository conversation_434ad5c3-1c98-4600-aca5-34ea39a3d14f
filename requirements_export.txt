# Additional requirements for quiz export functionality

# PDF Generation Libraries (choose one)
xhtml2pdf==0.2.11
# OR alternatively:
# weasyprint==60.2

# CSV is built into Python, no additional requirements needed

# Installation instructions:
# For xhtml2pdf (simpler, fewer dependencies):
# pip install xhtml2pdf

# For weasyprint (more features, better CSS support):
# pip install weasyprint

# Note: WeasyPrint may require additional system dependencies on some platforms:
# - On Ubuntu/Debian: sudo apt-get install python3-dev python3-pip python3-cffi python3-brotli libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0
# - On macOS: brew install pango
# - On Windows: Usually works out of the box with pip install

# Recommended: Use xhtml2pdf for simplicity unless advanced CSS features are needed
