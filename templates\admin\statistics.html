{% extends "base.html" %}

{% block title %}Admin - System Statistics{% endblock %}

{% block content %}
<div class="stats-dashboard">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="header-text">
                <h1><i class="fas fa-chart-line"></i> System Analytics Dashboard</h1>
                <p>Comprehensive insights into your quiz management system</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-refresh" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <a href="{{ url_for('admin_dashboard') }}" class="btn btn-back">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Key Metrics Overview -->
    <div class="metrics-overview">
        <div class="metric-card users-metric">
            <div class="metric-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ user_counts.admin + user_counts.teacher + user_counts.student + user_counts.parent }}</div>
                <div class="metric-label">Total Users</div>
                <div class="metric-change positive">
                    <i class="fas fa-arrow-up"></i> {{ verified_users }} verified
                </div>
            </div>
        </div>

        <div class="metric-card quizzes-metric">
            <div class="metric-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ total_quizzes }}</div>
                <div class="metric-label">Total Quizzes</div>
                <div class="metric-change positive">
                    <i class="fas fa-check-circle"></i> {{ active_quizzes }} active
                </div>
            </div>
        </div>

        <div class="metric-card attempts-metric">
            <div class="metric-icon">
                <i class="fas fa-pencil-alt"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ total_attempts }}</div>
                <div class="metric-label">Quiz Attempts</div>
                <div class="metric-change neutral">
                    <i class="fas fa-chart-bar"></i> {{ "%.1f"|format(avg_score) }}% avg score
                </div>
            </div>
        </div>

        <div class="metric-card questions-metric">
            <div class="metric-icon">
                <i class="fas fa-question-circle"></i>
            </div>
            <div class="metric-content">
                <div class="metric-value">{{ total_questions }}</div>
                <div class="metric-label">Total Questions</div>
                <div class="metric-change neutral">
                    <i class="fas fa-calculator"></i> {{ "%.1f"|format(total_questions / total_quizzes if total_quizzes > 0 else 0) }} per quiz
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-grid">
        <!-- User Distribution Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <h3><i class="fas fa-users"></i> User Distribution</h3>
                <div class="chart-controls">
                    <button class="chart-toggle active" data-chart="userChart">Pie</button>
                    <button class="chart-toggle" data-chart="userBarChart">Bar</button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="userChart"></canvas>
                <canvas id="userBarChart" style="display: none;"></canvas>
            </div>
            <div class="chart-legend">
                <div class="legend-item">
                    <span class="legend-color" style="background: #FF6384;"></span>
                    <span>Admins: {{ user_counts.admin }}</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color" style="background: #36A2EB;"></span>
                    <span>Teachers: {{ user_counts.teacher }}</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color" style="background: #FFCE56;"></span>
                    <span>Students: {{ user_counts.student }}</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color" style="background: #4BC0C0;"></span>
                    <span>Parents: {{ user_counts.parent }}</span>
                </div>
            </div>
        </div>

        <!-- Monthly Activity Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <h3><i class="fas fa-chart-line"></i> Monthly Quiz Activity</h3>
                <div class="chart-info">
                    <span class="info-badge">Last 6 Months</span>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="monthlyChart"></canvas>
            </div>
        </div>

        <!-- Grade Distribution Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <h3><i class="fas fa-medal"></i> Grade Distribution</h3>
                <div class="chart-controls">
                    <button class="chart-toggle active" data-chart="gradeChart">Doughnut</button>
                    <button class="chart-toggle" data-chart="gradeBarChart">Bar</button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="gradeChart"></canvas>
                <canvas id="gradeBarChart" style="display: none;"></canvas>
            </div>
        </div>

        <!-- Daily Activity Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <h3><i class="fas fa-calendar-day"></i> Daily Activity</h3>
                <div class="chart-info">
                    <span class="info-badge">Last 30 Days</span>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="activityChart"></canvas>
            </div>
        </div>

        <!-- Quiz Difficulty Distribution -->
        <div class="chart-card">
            <div class="chart-header">
                <h3><i class="fas fa-layer-group"></i> Quiz Difficulty</h3>
            </div>
            <div class="chart-container">
                <canvas id="difficultyChart"></canvas>
            </div>
        </div>

        <!-- System Health -->
        <div class="chart-card system-health">
            <div class="chart-header">
                <h3><i class="fas fa-heartbeat"></i> System Health</h3>
            </div>
            <div class="health-metrics">
                <div class="health-item">
                    <div class="health-icon verified">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="health-content">
                        <div class="health-value">{{ verified_users }}</div>
                        <div class="health-label">Verified Users</div>
                    </div>
                </div>
                <div class="health-item">
                    <div class="health-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="health-content">
                        <div class="health-value">{{ unverified_users }}</div>
                        <div class="health-label">Pending Verification</div>
                    </div>
                </div>
                <div class="health-item">
                    <div class="health-icon active">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="health-content">
                        <div class="health-value">{{ active_quizzes }}</div>
                        <div class="health-label">Active Quizzes</div>
                    </div>
                </div>
                <div class="health-item">
                    <div class="health-icon locked">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="health-content">
                        <div class="health-value">{{ locked_quizzes }}</div>
                        <div class="health-label">Locked Quizzes</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h2>Recent Activity</h2>
            </div>
            <div class="card-body">
                <div class="activity-section">
                    <div class="section-header">
                        <h3>Recent Quiz Attempts</h3>
                        <a href="{{ url_for('admin_quiz_attempts') }}" class="btn btn-sm btn-primary">View All Attempts</a>
                    </div>
                    {% if recent_attempts %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Quiz</th>
                                        <th>Score</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for attempt in recent_attempts %}
                                    <tr>
                                        <td>{{ attempt.student.name }}</td>
                                        <td>{{ attempt.quiz.title }}</td>
                                        <td>{{ "%.1f"|format(attempt.score) }}%</td>
                                        <td>{{ attempt.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No recent quiz attempts.</p>
                    {% endif %}
                </div>

                <div class="activity-section">
                    <h3>Recent Messages</h3>
                    {% if recent_messages %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Subject</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in recent_messages %}
                                    <tr>
                                        <td>{{ message.sender.name }}</td>
                                        <td>{{ message.receiver.name }}</td>
                                        <td>{{ message.subject or '(No Subject)' }}</td>
                                        <td>{{ message.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="no-data">
                            <i class="fas fa-info-circle"></i>
                            <p>No recent messages</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tables Section -->
    <div class="data-section">
        <!-- Top Performers -->
        <div class="data-card leaderboard">
            <div class="data-header">
                <h3><i class="fas fa-trophy"></i> Top Performing Students</h3>
                <div class="data-info">
                    <span class="info-badge">Minimum 3 attempts</span>
                </div>
            </div>
            <div class="leaderboard-content">
                {% if top_students %}
                    {% for student in top_students %}
                    <div class="leaderboard-item">
                        <div class="rank">
                            {% if loop.index == 1 %}
                                <i class="fas fa-crown gold"></i>
                            {% elif loop.index == 2 %}
                                <i class="fas fa-medal silver"></i>
                            {% elif loop.index == 3 %}
                                <i class="fas fa-medal bronze"></i>
                            {% else %}
                                <span class="rank-number">{{ loop.index }}</span>
                            {% endif %}
                        </div>
                        <div class="student-info">
                            <div class="student-name">{{ student.name }}</div>
                            <div class="student-stats">{{ student.attempt_count }} attempts</div>
                        </div>
                        <div class="score-badge">
                            {{ "%.1f"|format(student.avg_score) }}%
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-info-circle"></i>
                        <p>No students with sufficient attempts yet</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Teacher Performance -->
        <div class="data-card">
            <div class="data-header">
                <h3><i class="fas fa-chalkboard-teacher"></i> Teacher Performance</h3>
            </div>
            <div class="table-container">
                {% if teacher_stats %}
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>Teacher</th>
                                <th>Quizzes</th>
                                <th>Total Attempts</th>
                                <th>Avg Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for teacher in teacher_stats %}
                            <tr>
                                <td>
                                    <div class="teacher-cell">
                                        <i class="fas fa-user-tie"></i>
                                        {{ teacher.name }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ teacher.quiz_count }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ teacher.total_attempts or 0 }}</span>
                                </td>
                                <td>
                                    <div class="score-cell">
                                        <span class="score-value">{{ "%.1f"|format(teacher.avg_score or 0) }}%</span>
                                        <div class="score-bar">
                                            <div class="score-fill" style="width: {{ teacher.avg_score or 0 }}%;"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-info-circle"></i>
                        <p>No teacher data available</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="data-card recent-activity">
            <div class="data-header">
                <h3><i class="fas fa-clock"></i> Recent Activity</h3>
                <div class="activity-tabs">
                    <button class="tab-btn active" data-tab="attempts">Quiz Attempts</button>
                    <button class="tab-btn" data-tab="messages">Messages</button>
                </div>
            </div>

            <!-- Recent Attempts Tab -->
            <div class="tab-content active" id="attempts-tab">
                {% if recent_attempts %}
                    <div class="activity-list">
                        {% for attempt in recent_attempts %}
                        <div class="activity-item">
                            <div class="activity-icon quiz-attempt">
                                <i class="fas fa-pencil-alt"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ attempt.student.name }} completed "{{ attempt.quiz.title }}"</div>
                                <div class="activity-meta">
                                    <span class="score-badge {{ 'high' if attempt.score >= 80 else 'medium' if attempt.score >= 60 else 'low' }}">
                                        {{ "%.1f"|format(attempt.score) }}%
                                    </span>
                                    <span class="time-ago">{{ attempt.submitted_at.strftime('%m/%d %H:%M') }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="view-all">
                        <a href="{{ url_for('admin_quiz_attempts') }}" class="btn btn-outline">
                            <i class="fas fa-external-link-alt"></i> View All Attempts
                        </a>
                    </div>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-info-circle"></i>
                        <p>No recent quiz attempts</p>
                    </div>
                {% endif %}
            </div>

            <!-- Recent Messages Tab -->
            <div class="tab-content" id="messages-tab">
                {% if recent_messages %}
                    <div class="activity-list">
                        {% for message in recent_messages %}
                        <div class="activity-item">
                            <div class="activity-icon message">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ message.sender.name }} → {{ message.receiver.name }}</div>
                                <div class="activity-subtitle">{{ message.subject | default('(No Subject)', true) | truncate(40) }}</div>
                                <div class="activity-meta">
                                    <span class="role-badge {{ message.sender.role }}">{{ message.sender.role.title() }}</span>
                                    <span class="time-ago">{{ message.timestamp.strftime('%m/%d %H:%M') }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="view-all">
                        <a href="{{ url_for('inbox') }}" class="btn btn-outline">
                            <i class="fas fa-external-link-alt"></i> View All Messages
                        </a>
                    </div>
                {% else %}
                    <div class="no-data">
                        <i class="fas fa-info-circle"></i>
                        <p>No recent messages</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<script>
// Chart configurations and data
const chartColors = {
    primary: '#007bff',
    success: '#28a745',
    info: '#17a2b8',
    warning: '#ffc107',
    danger: '#dc3545',
    secondary: '#6c757d',
    light: '#f8f9fa',
    dark: '#343a40'
};

const gradientColors = {
    blue: ['#667eea', '#764ba2'],
    green: ['#f093fb', '#f5576c'],
    purple: ['#4facfe', '#00f2fe'],
    orange: ['#43e97b', '#38f9d7']
};

// Initialize all charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    initializeInteractivity();
});

function initializeCharts() {
    // User Distribution Pie Chart
    const userCtx = document.getElementById('userChart').getContext('2d');
    const userGradient = userCtx.createLinearGradient(0, 0, 0, 400);
    userGradient.addColorStop(0, 'rgba(255, 99, 132, 0.8)');
    userGradient.addColorStop(1, 'rgba(255, 99, 132, 0.2)');

    new Chart(userCtx, {
        type: 'doughnut',
        data: {
            labels: ['Admins', 'Teachers', 'Students', 'Parents'],
            datasets: [{
                data: [{{ user_counts.admin }}, {{ user_counts.teacher }}, {{ user_counts.student }}, {{ user_counts.parent }}],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0'
                ],
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#fff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true
                }
            },
            cutout: '60%',
            animation: {
                animateRotate: true,
                duration: 2000
            }
        }
    });

    // User Distribution Bar Chart (hidden by default)
    const userBarCtx = document.getElementById('userBarChart').getContext('2d');
    new Chart(userBarCtx, {
        type: 'bar',
        data: {
            labels: ['Admins', 'Teachers', 'Students', 'Parents'],
            datasets: [{
                data: [{{ user_counts.admin }}, {{ user_counts.teacher }}, {{ user_counts.student }}, {{ user_counts.parent }}],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ],
                borderColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0'
                ],
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Monthly Activity Line Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    const monthlyGradient = monthlyCtx.createLinearGradient(0, 0, 0, 400);
    monthlyGradient.addColorStop(0, 'rgba(54, 162, 235, 0.8)');
    monthlyGradient.addColorStop(1, 'rgba(54, 162, 235, 0.1)');

    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: {{ monthly_labels | tojson }},
            datasets: [{
                label: 'Quiz Attempts',
                data: {{ monthly_data | tojson }},
                borderColor: '#36A2EB',
                backgroundColor: monthlyGradient,
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#36A2EB',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#36A2EB',
                    borderWidth: 1,
                    cornerRadius: 8
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#666'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#666'
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });

    // Grade Distribution Doughnut Chart
    {% if grade_data.labels %}
    const gradeCtx = document.getElementById('gradeChart').getContext('2d');
    new Chart(gradeCtx, {
        type: 'doughnut',
        data: {
            labels: {{ grade_data.labels | tojson }},
            datasets: [{
                data: {{ grade_data.data | tojson }},
                backgroundColor: {{ grade_data.colors | tojson }},
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                }
            },
            cutout: '50%'
        }
    });

    // Grade Distribution Bar Chart (hidden by default)
    const gradeBarCtx = document.getElementById('gradeBarChart').getContext('2d');
    new Chart(gradeBarCtx, {
        type: 'bar',
        data: {
            labels: {{ grade_data.labels | tojson }},
            datasets: [{
                data: {{ grade_data.data | tojson }},
                backgroundColor: {{ grade_data.colors | tojson }},
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // Daily Activity Chart
    const activityCtx = document.getElementById('activityChart').getContext('2d');
    const activityGradient = activityCtx.createLinearGradient(0, 0, 0, 400);
    activityGradient.addColorStop(0, 'rgba(255, 206, 86, 0.8)');
    activityGradient.addColorStop(1, 'rgba(255, 206, 86, 0.1)');

    new Chart(activityCtx, {
        type: 'bar',
        data: {
            labels: {{ activity_labels | tojson }},
            datasets: [{
                label: 'Daily Attempts',
                data: {{ activity_data | tojson }},
                backgroundColor: activityGradient,
                borderColor: '#FFCE56',
                borderWidth: 2,
                borderRadius: 6,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Quiz Difficulty Chart
    {% if difficulty_data.labels %}
    const difficultyCtx = document.getElementById('difficultyChart').getContext('2d');
    new Chart(difficultyCtx, {
        type: 'polarArea',
        data: {
            labels: {{ difficulty_data.labels | tojson }},
            datasets: [{
                data: {{ difficulty_data.data | tojson }},
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
    {% endif %}
}

function initializeInteractivity() {
    // Chart toggle functionality
    document.querySelectorAll('.chart-toggle').forEach(button => {
        button.addEventListener('click', function() {
            const chartType = this.dataset.chart;
            const container = this.closest('.chart-card');
            const charts = container.querySelectorAll('canvas');
            const toggles = container.querySelectorAll('.chart-toggle');

            // Hide all charts
            charts.forEach(chart => chart.style.display = 'none');

            // Show selected chart
            document.getElementById(chartType).style.display = 'block';

            // Update toggle states
            toggles.forEach(toggle => toggle.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Activity tabs functionality
    document.querySelectorAll('.tab-btn').forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            const container = this.closest('.recent-activity');

            // Update tab buttons
            container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Update tab content
            container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            container.querySelector(`#${tabName}-tab`).classList.add('active');
        });
    });

    // Refresh dashboard functionality
    window.refreshDashboard = function() {
        const refreshBtn = document.querySelector('.btn-refresh');
        const icon = refreshBtn.querySelector('i');

        // Add spinning animation
        icon.classList.add('fa-spin');
        refreshBtn.disabled = true;

        // Simulate refresh (in real app, this would reload data)
        setTimeout(() => {
            icon.classList.remove('fa-spin');
            refreshBtn.disabled = false;

            // Show success message
            showNotification('Dashboard refreshed successfully!', 'success');
        }, 1500);
    };

    // Notification system
    window.showNotification = function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);

        // Hide notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    };

    // Add hover effects to metric cards
    document.querySelectorAll('.metric-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}
</script>

<style>
/* Modern Dashboard Styles */
* {
    box-sizing: border-box;
}

.stats-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.dashboard-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-text h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text p {
    margin: 0.5rem 0 0 0;
    color: #6c757d;
    font-size: 1.1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-refresh {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-refresh:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.btn-back {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Metrics Overview */
.metrics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.users-metric .metric-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.quizzes-metric .metric-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.attempts-metric .metric-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.questions-metric .metric-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    line-height: 1;
}

.metric-label {
    font-size: 1rem;
    color: #6c757d;
    margin: 0.5rem 0;
    font-weight: 500;
}

.metric-change {
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.neutral {
    color: #6c757d;
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.chart-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-toggle {
    padding: 0.4rem 0.8rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.chart-toggle.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.chart-toggle:hover:not(.active) {
    border-color: #667eea;
    color: #667eea;
}

.chart-container {
    height: 300px;
    position: relative;
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f8f9fa;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.chart-info {
    display: flex;
    gap: 0.5rem;
}

.info-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* System Health */
.system-health .chart-container {
    height: auto;
}

.health-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.health-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.health-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.health-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.health-icon.verified {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.health-icon.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.health-icon.active {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
}

.health-icon.locked {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

.health-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.health-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0.25rem 0 0 0;
}

/* Data Section */
.data-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.data-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.data-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.data-info {
    display: flex;
    gap: 0.5rem;
}

/* Leaderboard */
.leaderboard-content {
    max-height: 400px;
    overflow-y: auto;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.leaderboard-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.rank {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: 700;
    font-size: 1.2rem;
}

.rank .fa-crown.gold {
    color: #ffd700;
    font-size: 1.5rem;
}

.rank .fa-medal.silver {
    color: #c0c0c0;
    font-size: 1.3rem;
}

.rank .fa-medal.bronze {
    color: #cd7f32;
    font-size: 1.3rem;
}

.rank-number {
    background: #6c757d;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.student-info {
    flex: 1;
}

.student-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.student-stats {
    font-size: 0.85rem;
    color: #6c757d;
}

.score-badge {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Modern Table */
.table-container {
    max-height: 400px;
    overflow-y: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table th,
.modern-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #f8f9fa;
}

.modern-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
    z-index: 1;
}

.modern-table tbody tr:hover {
    background: #f8f9fa;
}

.teacher-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-primary {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
}

.badge-info {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
}

.score-cell {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.score-value {
    font-weight: 600;
    color: #2c3e50;
}

.score-bar {
    width: 100px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Activity Tabs */
.activity-tabs {
    display: flex;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.tab-btn:hover:not(.active) {
    border-color: #667eea;
    color: #667eea;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Activity List */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e9ecef;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.activity-icon.quiz-attempt {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.activity-icon.message {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.activity-subtitle {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.activity-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.score-badge.high {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.score-badge.medium {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.score-badge.low {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.role-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.role-badge.teacher {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.role-badge.student {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

.role-badge.parent {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.role-badge.admin {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
}

.time-ago {
    font-size: 0.75rem;
    color: #6c757d;
}

.view-all {
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f8f9fa;
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* No Data State */
.no-data {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.no-data i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-data p {
    margin: 0;
    font-style: italic;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transform: translateX(400px);
    transition: all 0.3s ease;
    z-index: 1000;
    border-left: 4px solid;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left-color: #28a745;
    color: #155724;
}

.notification-info {
    border-left-color: #17a2b8;
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }

    .data-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .stats-dashboard {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .header-text h1 {
        font-size: 2rem;
    }

    .metrics-overview {
        grid-template-columns: 1fr;
    }

    .metric-card {
        padding: 1.5rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-container {
        height: 250px;
    }

    .activity-tabs {
        flex-direction: column;
    }

    .activity-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn {
        justify-content: center;
    }

    .metric-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .leaderboard-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .activity-item {
        flex-direction: column;
        gap: 0.75rem;
    }
}
</style>
{% endblock %}