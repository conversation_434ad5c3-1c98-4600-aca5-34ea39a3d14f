{% extends "base.html" %}

{% block title %}Quiz Versions: {{ quiz.title }}{% endblock %}

{% block content %}
<div class="container quiz-versions-container">
    <div class="quiz-header">
        <h1>Version History: {{ quiz.title }}</h1>
        <a href="{{ url_for('view_quiz', quiz_id=quiz.id) }}" class="btn btn-secondary">Back to Quiz</a>
    </div>

    <div class="versions-overview">
        <p>This page shows all versions of the quiz "{{ quiz.title }}". Each version represents a snapshot of the quiz at the time it was created.</p>
    </div>

    <div class="versions-list">
        {% for version in all_versions %}
        <div class="version-card {% if version.is_locked %}locked{% endif %} {% if version.is_active %}active{% endif %}">
            <div class="version-header">
                <div class="version-info">
                    <h3>
                        Version {{ version.version_number }}
                        {% if version.original_quiz_id %}
                            <span class="revision-badge">Revision</span>
                        {% else %}
                            <span class="original-badge">Original</span>
                        {% endif %}
                    </h3>
                    <p class="version-title">{{ version.title }}</p>
                </div>
                <div class="version-status">
                    {% if version.is_locked %}
                        <span class="status-badge locked">🔒 Locked</span>
                    {% elif version.is_active %}
                        <span class="status-badge active">✅ Active</span>
                    {% else %}
                        <span class="status-badge inactive">⏸️ Inactive</span>
                    {% endif %}
                </div>
            </div>

            <div class="version-details">
                <div class="detail-grid">
                    <div class="detail-item">
                        <strong>Created:</strong>
                        <span>{{ version.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% if version.locked_at %}
                    <div class="detail-item">
                        <strong>Locked:</strong>
                        <span>{{ version.locked_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% endif %}
                    <div class="detail-item">
                        <strong>Questions:</strong>
                        <span>{{ version.questions|length }}</span>
                    </div>
                    <div class="detail-item">
                        <strong>Total Marks:</strong>
                        <span>{{ version.total_marks }}</span>
                    </div>
                    <div class="detail-item">
                        <strong>Time Limit:</strong>
                        <span>{{ version.time_limit }} minutes</span>
                    </div>
                    <div class="detail-item">
                        <strong>Difficulty:</strong>
                        <span class="difficulty-badge {{ version.difficulty }}">{{ version.difficulty|title }}</span>
                    </div>
                </div>
            </div>

            <div class="version-stats">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value">{{ version.attempt_count }}</span>
                        <span class="stat-label">Attempts</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">{{ "%.1f"|format(version.avg_score) }}%</span>
                        <span class="stat-label">Avg Score</span>
                    </div>
                </div>
            </div>

            <div class="version-actions">
                <a href="{{ url_for('view_quiz', quiz_id=version.id) }}" class="btn btn-primary">View Details</a>
                {% if not version.is_locked and version.is_active %}
                    <a href="{{ url_for('edit_quiz', quiz_id=version.id) }}" class="btn btn-secondary">
                        {% if version.attempt_count > 0 %}Edit (Will Create New Version){% else %}Edit{% endif %}
                    </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<style>
.quiz-versions-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.quiz-header h1 {
    margin: 0;
    color: #343a40;
}

.versions-overview {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 2rem;
    border-left: 4px solid #007bff;
}

.versions-list {
    display: grid;
    gap: 1.5rem;
}

.version-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.version-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.version-card.locked {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.version-card.active {
    border-color: #28a745;
    border-width: 2px;
}

.version-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.version-info h3 {
    margin: 0 0 0.5rem 0;
    color: #495057;
}

.version-title {
    margin: 0;
    color: #6c757d;
    font-style: italic;
}

.revision-badge, .original-badge {
    font-size: 0.8em;
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
    margin-left: 8px;
}

.revision-badge {
    background-color: #17a2b8;
}

.original-badge {
    background-color: #6f42c1;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: bold;
}

.status-badge.locked {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #e2e3e5;
    color: #383d41;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.difficulty-badge {
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
    font-size: 0.8em;
}

.difficulty-badge.easy { background-color: #28a745; }
.difficulty-badge.medium { background-color: #ffc107; color: #212529; }
.difficulty-badge.hard { background-color: #dc3545; }

.stats-grid {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5em;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    display: block;
    font-size: 0.9em;
    color: #6c757d;
}

.version-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}
</style>
{% endblock %}
