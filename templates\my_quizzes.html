<!DOCTYPE html>
<html>
<head>
    <title>My Quizzes - Quiz Management System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .quiz-badges {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .version-badge {
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active { background: #d4edda; color: #155724; }
        .status-locked { background: #f8d7da; color: #721c24; }
        .status-inactive { background: #e2e3e5; color: #6c757d; }

        .locked-quiz {
            opacity: 0.8;
            border-left: 4px solid #dc3545;
        }

        .version-info {
            margin: 0.5rem 0;
        }

        .version-info small {
            color: #6c757d;
            font-style: italic;
        }

        .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .quiz-meta span {
            margin-right: 1rem;
        }

        .quiz-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .quiz-header h3 {
            margin: 0;
            flex: 1;
        }

        /* Export Dropdown Styles */
        .export-dropdown {
            position: relative;
            display: inline-block;
        }

        .btn-export-toggle {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
        }

        .btn-export-toggle:hover {
            background: linear-gradient(135deg, #218838, #1e7e34);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .btn-export-toggle i:last-child {
            transition: transform 0.3s ease;
        }

        .btn-export-toggle.active i:last-child {
            transform: rotate(180deg);
        }

        .export-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            min-width: 220px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .export-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .export-option {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            text-decoration: none;
            color: #495057;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.3s ease;
        }

        .export-option:last-child {
            border-bottom: none;
        }

        .export-option:hover {
            background: #f8f9fa;
            color: #495057;
            text-decoration: none;
        }

        .export-option i {
            font-size: 1.5rem;
            width: 24px;
            text-align: center;
        }

        .csv-option i {
            color: #28a745;
        }

        .pdf-option i {
            color: #dc3545;
        }

        .export-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .export-title {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .export-desc {
            font-size: 0.8rem;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .quiz-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .quiz-badges {
                width: 100%;
                justify-content: flex-start;
            }

            .quiz-actions {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .export-menu {
                right: auto;
                left: 0;
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>My Quizzes</h1>
            <nav>
                <a href="/teacher/dashboard" class="btn">Back to Dashboard</a>
                <a href="/logout" class="btn btn-danger">Logout</a>
            </nav>
        </div>
    </header>
    
    <main class="container">
        <section class="dashboard-section">
            <div class="section-header">
                <h2>My Created Quizzes</h2>
                <a href="/teacher/create-quiz" class="btn">Create New Quiz</a>
            </div>
            
            <div class="quizzes-grid">
                {% for quiz in quizzes %}
                <div class="quiz-card {% if quiz.is_locked %}locked-quiz{% endif %}">
                    <div class="quiz-header">
                        <h3>{{ quiz.title }}</h3>
                        <div class="quiz-badges">
                            <span class="difficulty-badge {{ quiz.difficulty }}">{{ quiz.difficulty|title }}</span>
                            <span class="version-badge">v{{ quiz.version_number }}</span>
                            {% if quiz.is_locked %}
                                <span class="status-badge status-locked">Locked</span>
                            {% elif quiz.is_active %}
                                <span class="status-badge status-active">Active</span>
                            {% else %}
                                <span class="status-badge status-inactive">Inactive</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="quiz-details">
                        <p class="quiz-description">{{ quiz.description or 'No description provided' }}</p>
                        <div class="quiz-meta">
                            <span>Time: {{ quiz.time_limit }} mins</span>
                            <span>Total Marks: {{ quiz.total_marks }}</span>
                            <span>Questions: {{ quiz.questions|length }}</span>
                            <span>Attempts: {{ quiz.attempt_count }}</span>
                            <span class="version-info">
                                Version: <strong>v{{ quiz.version_number }}</strong>
                                {% if quiz.original_quiz_id %}
                                    <span class="revision-badge">(Revision)</span>
                                {% endif %}
                            </span>
                            <span class="status-info">
                                Status:
                                {% if quiz.is_locked %}
                                    <span class="status-locked">🔒 Locked</span>
                                {% elif quiz.is_active %}
                                    <span class="status-active">✅ Active</span>
                                {% else %}
                                    <span class="status-inactive">⏸️ Inactive</span>
                                {% endif %}
                            </span>
                        </div>
                        <div class="grade-thresholds">
                            <span>A: ≥{{ quiz.grade_a_threshold }}%</span>
                            <span>B: ≥{{ quiz.grade_b_threshold }}%</span>
                            <span>C: ≥{{ quiz.grade_c_threshold }}%</span>
                            <span>D: ≥{{ quiz.grade_d_threshold }}%</span>
                        </div>
                    </div>

                    <div class="quiz-actions">
                        {% if quiz.is_locked %}
                            <span class="btn btn-secondary disabled" title="Quiz is locked due to existing attempts">Edit (Locked)</span>
                        {% else %}
                            <a href="/teacher/edit-quiz/{{ quiz.id }}" class="btn btn-secondary">
                                {% if quiz.attempt_count > 0 %}Edit (Will Create New Version){% else %}Edit{% endif %}
                            </a>
                        {% endif %}
                        <a href="/teacher/view-quiz/{{ quiz.id }}" class="btn">View</a>

                        <!-- Export Buttons -->
                        <div class="export-dropdown">
                            <button class="btn btn-export-toggle" onclick="toggleExportMenu({{ quiz.id }})">
                                <i class="fas fa-download"></i> Export
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="export-menu" id="export-menu-{{ quiz.id }}">
                                <a href="{{ url_for('export_quiz_csv', quiz_id=quiz.id) }}" class="export-option csv-option">
                                    <i class="fas fa-file-csv"></i>
                                    <div class="export-info">
                                        <span class="export-title">CSV Format</span>
                                        <span class="export-desc">Spreadsheet-friendly data</span>
                                    </div>
                                </a>
                                <a href="{{ url_for('export_quiz_pdf', quiz_id=quiz.id) }}" class="export-option pdf-option">
                                    <i class="fas fa-file-pdf"></i>
                                    <div class="export-info">
                                        <span class="export-title">PDF Format</span>
                                        <span class="export-desc">Print-ready document</span>
                                    </div>
                                </a>
                            </div>
                        </div>

                        {% if quiz.is_locked %}
                            <span class="btn btn-danger disabled" title="Cannot delete locked quiz">Delete (Locked)</span>
                        {% else %}
                            <button onclick="deleteQuiz({{ quiz.id }})" class="btn btn-danger">Delete</button>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="no-quizzes">
                    <p>You haven't created any quizzes yet.</p>
                    <a href="/teacher/create-quiz" class="btn">Create Your First Quiz</a>
                </div>
                {% endfor %}
            </div>
        </section>
    </main>

    <style>
        .version-info {
            color: #6c757d;
            font-size: 0.9em;
        }

        .revision-badge {
            background-color: #17a2b8;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-left: 5px;
        }

        .status-info {
            font-size: 0.9em;
        }

        .status-locked {
            color: #dc3545;
            font-weight: bold;
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
        }

        .status-inactive {
            color: #6c757d;
            font-weight: bold;
        }

        .quiz-meta span {
            margin-right: 15px;
            display: inline-block;
        }
    </style>

    <script>
        function deleteQuiz(quizId) {
            if (confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
                fetch(`/teacher/delete-quiz/${quizId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Failed to delete quiz. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the quiz.');
                });
            }
        }

        // Export dropdown functionality
        function toggleExportMenu(quizId) {
            const menu = document.getElementById(`export-menu-${quizId}`);
            const button = menu.previousElementSibling;

            // Close all other export menus
            document.querySelectorAll('.export-menu').forEach(otherMenu => {
                if (otherMenu !== menu) {
                    otherMenu.classList.remove('show');
                    otherMenu.previousElementSibling.classList.remove('active');
                }
            });

            // Toggle current menu
            menu.classList.toggle('show');
            button.classList.toggle('active');
        }

        // Close export menus when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.export-dropdown')) {
                document.querySelectorAll('.export-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.previousElementSibling.classList.remove('active');
                });
            }
        });

        // Add loading state to export links
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.export-option').forEach(link => {
                link.addEventListener('click', function(e) {
                    const originalText = this.querySelector('.export-title').textContent;
                    const titleElement = this.querySelector('.export-title');

                    // Show loading state
                    titleElement.textContent = 'Generating...';
                    this.style.opacity = '0.7';
                    this.style.pointerEvents = 'none';

                    // Reset after a delay (file download should start)
                    setTimeout(() => {
                        titleElement.textContent = originalText;
                        this.style.opacity = '1';
                        this.style.pointerEvents = 'auto';

                        // Close the dropdown
                        const menu = this.closest('.export-menu');
                        menu.classList.remove('show');
                        menu.previousElementSibling.classList.remove('active');
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html> 