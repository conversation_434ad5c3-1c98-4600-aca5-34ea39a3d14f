<!DOCTYPE html>
<html>
<head>
    <title>My Quizzes - Quiz Management System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .quiz-badges {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .version-badge {
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active { background: #d4edda; color: #155724; }
        .status-locked { background: #f8d7da; color: #721c24; }
        .status-inactive { background: #e2e3e5; color: #6c757d; }

        .locked-quiz {
            opacity: 0.8;
            border-left: 4px solid #dc3545;
        }

        .version-info {
            margin: 0.5rem 0;
        }

        .version-info small {
            color: #6c757d;
            font-style: italic;
        }

        .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .quiz-meta span {
            margin-right: 1rem;
        }

        .quiz-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .quiz-header h3 {
            margin: 0;
            flex: 1;
        }

        @media (max-width: 768px) {
            .quiz-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .quiz-badges {
                width: 100%;
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>My Quizzes</h1>
            <nav>
                <a href="/teacher/dashboard" class="btn">Back to Dashboard</a>
                <a href="/logout" class="btn btn-danger">Logout</a>
            </nav>
        </div>
    </header>
    
    <main class="container">
        <section class="dashboard-section">
            <div class="section-header">
                <h2>My Created Quizzes</h2>
                <a href="/teacher/create-quiz" class="btn">Create New Quiz</a>
            </div>
            
            <div class="quizzes-grid">
                {% for quiz in quizzes %}
                <div class="quiz-card {% if quiz.is_locked %}locked-quiz{% endif %}">
                    <div class="quiz-header">
                        <h3>{{ quiz.title }}</h3>
                        <div class="quiz-badges">
                            <span class="difficulty-badge {{ quiz.difficulty }}">{{ quiz.difficulty|title }}</span>
                            <span class="version-badge">v{{ quiz.version_number }}</span>
                            {% if quiz.is_locked %}
                                <span class="status-badge status-locked">Locked</span>
                            {% elif quiz.is_active %}
                                <span class="status-badge status-active">Active</span>
                            {% else %}
                                <span class="status-badge status-inactive">Inactive</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="quiz-details">
                        <p class="quiz-description">{{ quiz.description or 'No description provided' }}</p>
                        <div class="quiz-meta">
                            <span>Time: {{ quiz.time_limit }} mins</span>
                            <span>Total Marks: {{ quiz.total_marks }}</span>
                            <span>Questions: {{ quiz.questions|length }}</span>
                            <span>Attempts: {{ quiz.attempt_count }}</span>
                        </div>
                        {% if quiz.original_quiz_id %}
                            <div class="version-info">
                                <small>This is a revision of the original quiz</small>
                            </div>
                        {% endif %}
                        <div class="grade-thresholds">
                            <span>A: ≥{{ quiz.grade_a_threshold }}%</span>
                            <span>B: ≥{{ quiz.grade_b_threshold }}%</span>
                            <span>C: ≥{{ quiz.grade_c_threshold }}%</span>
                            <span>D: ≥{{ quiz.grade_d_threshold }}%</span>
                        </div>
                    </div>

                    <div class="quiz-actions">
                        {% if quiz.is_locked %}
                            <span class="btn btn-secondary disabled" title="Quiz is locked due to existing attempts">Edit (Locked)</span>
                        {% else %}
                            <a href="/teacher/edit-quiz/{{ quiz.id }}" class="btn btn-secondary">
                                {% if quiz.attempt_count > 0 %}Edit (Will Create New Version){% else %}Edit{% endif %}
                            </a>
                        {% endif %}
                        <a href="/teacher/view-quiz/{{ quiz.id }}" class="btn">View</a>
                        {% if quiz.is_locked %}
                            <span class="btn btn-danger disabled" title="Cannot delete locked quiz">Delete (Locked)</span>
                        {% else %}
                            <button onclick="deleteQuiz({{ quiz.id }})" class="btn btn-danger">Delete</button>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="no-quizzes">
                    <p>You haven't created any quizzes yet.</p>
                    <a href="/teacher/create-quiz" class="btn">Create Your First Quiz</a>
                </div>
                {% endfor %}
            </div>
        </section>
    </main>

    <script>
        function deleteQuiz(quizId) {
            if (confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
                fetch(`/teacher/delete-quiz/${quizId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Failed to delete quiz. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the quiz.');
                });
            }
        }
    </script>
</body>
</html> 