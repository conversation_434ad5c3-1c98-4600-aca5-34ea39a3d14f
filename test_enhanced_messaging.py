#!/usr/bin/env python3
"""
Test script for enhanced messaging system in quiz management application.
This script validates that the enhanced messaging features work correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, User, Message, send_message_to_student_and_parent, find_parent_for_student
from werkzeug.security import generate_password_hash
import uuid

def test_enhanced_messaging():
    """Test the enhanced messaging system"""
    with app.app_context():
        print("Testing Enhanced Messaging System")
        print("=" * 50)
        
        # Create test users
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Message Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
        
        # Create test parent
        parent = User.query.filter_by(email='<EMAIL>').first()
        if not parent:
            parent = User(
                name='Test Message Parent',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='parent',
                is_verified=True
            )
            db.session.add(parent)
        
        # Create test student linked to parent
        student = User.query.filter_by(email='<EMAIL>').first()
        if not student:
            student = User(
                name='Test Message Student',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                parent_email='<EMAIL>',
                is_verified=True
            )
            db.session.add(student)
        
        # Create test student without parent
        student_no_parent = User.query.filter_by(email='<EMAIL>').first()
        if not student_no_parent:
            student_no_parent = User(
                name='Test Student No Parent',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                parent_email=None,
                is_verified=True
            )
            db.session.add(student_no_parent)
        
        db.session.commit()
        print("✓ Created test users (teacher, parent, student with parent, student without parent)")
        
        # Test 1: Find parent for student
        print("\nTest 1: Testing parent-student relationship...")
        found_parent = find_parent_for_student(student)
        assert found_parent is not None, "Should find parent for student"
        assert found_parent.id == parent.id, "Should find correct parent"
        print("✓ Parent-student relationship works correctly")
        
        found_parent_none = find_parent_for_student(student_no_parent)
        assert found_parent_none is None, "Should not find parent for student without parent"
        print("✓ Student without parent handled correctly")
        
        # Test 2: Send message to student and parent
        print("\nTest 2: Testing enhanced messaging (student + parent)...")
        success, result = send_message_to_student_and_parent(
            teacher.id,
            student.id,
            "Test Subject: Math Assignment",
            "Dear Student and Parent,\n\nThis is a test message about the math assignment.\n\nBest regards,\nTeacher"
        )
        
        assert success, f"Message sending should succeed: {result}"
        assert len(result) == 2, "Should send to 2 recipients (student + parent)"
        
        recipients = [r[0] for r in result]
        assert 'student' in recipients, "Should send to student"
        assert 'parent' in recipients, "Should send to parent"
        print("✓ Enhanced messaging to student and parent works correctly")
        
        # Test 3: Send message to student without parent
        print("\nTest 3: Testing messaging to student without parent...")
        success, result = send_message_to_student_and_parent(
            teacher.id,
            student_no_parent.id,
            "Test Subject: Solo Student",
            "This message goes to student only."
        )
        
        assert success, f"Message sending should succeed: {result}"
        assert len(result) == 1, "Should send to 1 recipient (student only)"
        assert result[0][0] == 'student', "Should send to student"
        print("✓ Messaging to student without parent works correctly")
        
        # Test 4: Verify message properties
        print("\nTest 4: Testing message properties...")
        
        # Get messages for student
        student_messages = Message.query.filter_by(receiver_id=student.id).all()
        assert len(student_messages) > 0, "Student should have received messages"
        
        student_msg = student_messages[-1]  # Get latest message
        assert student_msg.is_parent_copy == False, "Student message should not be parent copy"
        assert student_msg.receiver_role == 'student', "Student message should have correct receiver role"
        assert student_msg.message_group_id is not None, "Student message should have group ID"
        print("✓ Student message properties are correct")
        
        # Get messages for parent
        parent_messages = Message.query.filter_by(receiver_id=parent.id).all()
        assert len(parent_messages) > 0, "Parent should have received messages"
        
        parent_msg = parent_messages[-1]  # Get latest message
        assert parent_msg.is_parent_copy == True, "Parent message should be parent copy"
        assert parent_msg.receiver_role == 'parent', "Parent message should have correct receiver role"
        assert parent_msg.original_recipient_id == student.id, "Parent message should reference original student"
        assert parent_msg.message_group_id == student_msg.message_group_id, "Messages should share group ID"
        print("✓ Parent message properties are correct")
        
        # Test 5: Test message privacy
        print("\nTest 5: Testing message privacy...")
        
        # Student should only see their own message
        student_inbox = Message.query.filter_by(receiver_id=student.id).all()
        for msg in student_inbox:
            assert msg.receiver_id == student.id, "Student should only see messages addressed to them"
        
        # Parent should only see their own message
        parent_inbox = Message.query.filter_by(receiver_id=parent.id).all()
        for msg in parent_inbox:
            assert msg.receiver_id == parent.id, "Parent should only see messages addressed to them"
        
        print("✓ Message privacy is maintained correctly")
        
        # Test 6: Test message grouping
        print("\nTest 6: Testing message grouping...")
        
        # Find related messages by group ID
        group_id = student_msg.message_group_id
        grouped_messages = Message.query.filter_by(message_group_id=group_id).all()
        
        assert len(grouped_messages) == 2, "Should have 2 messages in group (student + parent)"
        
        student_grouped = [m for m in grouped_messages if m.receiver_role == 'student']
        parent_grouped = [m for m in grouped_messages if m.receiver_role == 'parent']
        
        assert len(student_grouped) == 1, "Should have 1 student message in group"
        assert len(parent_grouped) == 1, "Should have 1 parent message in group"
        print("✓ Message grouping works correctly")
        
        # Test 7: Test traditional messaging still works
        print("\nTest 7: Testing traditional messaging compatibility...")
        
        traditional_msg = Message(
            sender_id=teacher.id,
            receiver_id=student.id,
            subject="Traditional Message",
            body="This is a traditional direct message.",
            receiver_role='student'
        )
        db.session.add(traditional_msg)
        db.session.commit()
        
        # Verify traditional message properties
        assert traditional_msg.is_parent_copy == False, "Traditional message should not be parent copy"
        assert traditional_msg.original_recipient_id is None, "Traditional message should not have original recipient"
        assert traditional_msg.message_group_id is None, "Traditional message should not have group ID"
        print("✓ Traditional messaging compatibility maintained")
        
        # Test 8: Test error handling
        print("\nTest 8: Testing error handling...")
        
        # Test with invalid student ID
        success, result = send_message_to_student_and_parent(
            teacher.id,
            99999,  # Non-existent student ID
            "Test Subject",
            "Test Body"
        )
        
        assert not success, "Should fail with invalid student ID"
        print("✓ Error handling for invalid student ID works correctly")
        
        # Test 9: Test message context for teachers
        print("\nTest 9: Testing teacher message context...")
        
        from app import get_message_context_for_teacher
        
        # Test context for parent copy message
        parent_context = get_message_context_for_teacher(parent_msg)
        assert parent_context['is_parent_copy'] == True, "Should identify parent copy"
        assert parent_context['original_recipient'] is not None, "Should have original recipient"
        assert parent_context['original_recipient'].id == student.id, "Should reference correct student"
        print("✓ Teacher context for parent copy message works correctly")
        
        # Test context for student message with related messages
        student_context = get_message_context_for_teacher(student_msg)
        assert student_context['is_parent_copy'] == False, "Should not be parent copy"
        assert len(student_context['related_messages']) > 0, "Should have related messages"
        print("✓ Teacher context for grouped messages works correctly")
        
        # Cleanup
        print("\nCleaning up test data...")
        try:
            # Delete test messages
            Message.query.filter_by(sender_id=teacher.id).delete()
            
            # Delete test users
            db.session.delete(student_no_parent)
            db.session.delete(student)
            db.session.delete(parent)
            db.session.delete(teacher)
            
            db.session.commit()
            print("✓ Test data cleaned up successfully")
        except Exception as e:
            print(f"Warning: Could not clean up all test data: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All enhanced messaging tests passed!")
        print("\nSummary:")
        print("- Parent-student relationship detection works")
        print("- Enhanced messaging to student and parent works")
        print("- Message privacy and role-based access works")
        print("- Message grouping and context works")
        print("- Traditional messaging compatibility maintained")
        print("- Error handling works correctly")
        print("- Teacher message context features work")
        
        return True

if __name__ == "__main__":
    try:
        success = test_enhanced_messaging()
        if success:
            print("\n✅ Enhanced messaging test completed successfully!")
        else:
            print("\n❌ Enhanced messaging test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
