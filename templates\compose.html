{% extends "base.html" %}

{% block title %}Compose Message{% endblock %}

{% block content %}
<div class="container compose-container">
    <div class="card-header">
        <h1>Compose New Message</h1>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('compose_message') }}">
            <!-- Enhanced messaging for teachers/admins -->
            {% if user_role in ['teacher', 'admin'] and available_students %}
            <div class="messaging-mode-selector">
                <h3>Message Type</h3>
                <div class="radio-group">
                    <label class="radio-option">
                        <input type="radio" name="message_mode" value="student" checked onchange="toggleMessageMode()">
                        <span class="radio-text">Send to Student (and Parent)</span>
                        <span class="radio-description">Message will be sent to the selected student and their linked parent automatically</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="message_mode" value="direct" onchange="toggleMessageMode()">
                        <span class="radio-text">Direct Message</span>
                        <span class="radio-description">Send a direct message to a specific user</span>
                    </label>
                </div>
            </div>

            <!-- Student selection mode -->
            <div id="student-mode" class="recipient-section">
                <div class="form-group">
                    <label for="student_id">Select Student:</label>
                    <select id="student_id" name="student_id" class="form-control">
                        <option value="">-- Choose a student --</option>
                        {% for student in available_students %}
                        <option value="{{ student.id }}">{{ student.name }} ({{ student.email }})</option>
                        {% endfor %}
                    </select>
                    <small class="form-text text-muted">The message will be sent to both the student and their linked parent (if available)</small>
                </div>
            </div>

            <!-- Direct message mode -->
            <div id="direct-mode" class="recipient-section" style="display: none;">
                <div class="form-group">
                    <label for="receiver_username">To (Username):</label>
                    <input type="text" id="receiver_username" name="receiver_username"
                           value="{{ recipient.name if recipient else '' }}"
                           class="form-control {% if recipient %}is-readonly{% endif %}"
                           {% if recipient %}readonly{% endif %}>
                    {% if recipient %}
                        <small class="form-text text-muted">Sending to {{ recipient.name }} ({{ recipient.role.capitalize() }})</small>
                    {% endif %}
                </div>
            </div>
            {% else %}
            <!-- Traditional messaging for other roles -->
            <div class="form-group">
                <label for="receiver_username">To (Username):</label>
                <input type="text" id="receiver_username" name="receiver_username"
                       value="{{ recipient.name if recipient else '' }}"
                       class="form-control {% if recipient %}is-readonly{% endif %}"
                       required {% if recipient %}readonly{% endif %}>
                {% if recipient %}
                    <small class="form-text text-muted">Sending to {{ recipient.name }} ({{ recipient.role.capitalize() }})</small>
                {% endif %}
            </div>
            {% endif %}
            <div class="form-group">
                <label for="subject">Subject:</label>
                <input type="text" id="subject" name="subject" value="{{ subject if subject else '' }}" class="form-control">
            </div>
            <div class="form-group">
                <label for="body">Message:</label>
                <textarea id="body" name="body" rows="10" required class="form-control">{{ body if body else '' }}</textarea>
            </div>
            <div class="form-actions">
                <a href="{{ url_for('inbox') }}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Send Message</button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleMessageMode() {
    const studentMode = document.getElementById('student-mode');
    const directMode = document.getElementById('direct-mode');
    const studentRadio = document.querySelector('input[name="message_mode"][value="student"]');
    const directRadio = document.querySelector('input[name="message_mode"][value="direct"]');

    if (studentRadio.checked) {
        studentMode.style.display = 'block';
        directMode.style.display = 'none';
        // Clear direct mode fields
        const usernameInput = document.getElementById('receiver_username');
        if (usernameInput) usernameInput.value = '';
    } else if (directRadio.checked) {
        studentMode.style.display = 'none';
        directMode.style.display = 'block';
        // Clear student mode fields
        const studentSelect = document.getElementById('student_id');
        if (studentSelect) studentSelect.value = '';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleMessageMode();
});
</script>

<style>
/* Shared styles can go in base.css if preferred */
.compose-container {
    max-width: 750px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    overflow: hidden; /* Contain header background */
}

.card-header {
    background-color: #007bff;
    color: white;
    padding: 1rem 1.5rem;
}

.card-header h1 {
    margin: 0;
    font-size: 1.5rem;
}

.card-body {
    padding: 2rem 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.form-control.is-readonly {
    background-color: #e9ecef;
    opacity: 1;
    cursor: not-allowed;
}

textarea.form-control {
    resize: vertical;
    min-height: 150px;
}

.form-text.text-muted {
    font-size: 0.875em;
    color: #6c757d;
    margin-top: 0.25rem;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.8rem;
}

.btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
    border-radius: 0.25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}

/* Enhanced messaging styles */
.messaging-mode-selector {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.messaging-mode-selector h3 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.2rem;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.radio-option {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background-color: white;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.radio-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.radio-option input[type="radio"] {
    margin: 0;
    margin-top: 0.2rem;
}

.radio-option input[type="radio"]:checked + .radio-text {
    color: #007bff;
    font-weight: 600;
}

.radio-option:has(input[type="radio"]:checked) {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.radio-text {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.radio-description {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

.recipient-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.recipient-section .form-group {
    margin-bottom: 0;
}

/* Select styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .radio-group {
        gap: 0.75rem;
    }

    .radio-option {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .radio-option input[type="radio"] {
        margin-top: 0;
    }
}
</style>
{% endblock %}