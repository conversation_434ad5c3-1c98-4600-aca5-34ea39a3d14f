{% extends "base.html" %}

{% block title %}Pending Users{% endblock %}

{% block content %}
<main class="page-content admin-panel">
    <div class="container">
        <h1>Pending User Verification</h1>
        <a href="{{ url_for('admin_dashboard') }}" class="back-link">&larr; Back to Admin Dashboard</a>

        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              <div class="flash-message {{ category }}">{{ message }}</div>
            {% endfor %}
          {% endif %}
        {% endwith %}

        <div class="pending-users-info">
            <p>The following users have registered and are awaiting verification. You can approve or reject each user.</p>
        </div>

        <table class="user-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Parent Email</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.name }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.role }}</td>
                    <td>{{ user.parent_email if user.parent_email else 'N/A' }}</td>
                    <td class="action-buttons">
                        <a href="{{ url_for('admin_verify_user', user_id=user.id, token=user.verification_token) }}" class="btn btn-approve">Approve</a>
                        <form action="{{ url_for('admin_reject_user', user_id=user.id) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to reject this user? This cannot be undone.');">
                            <button type="submit" class="btn btn-reject">Reject</button>
                        </form>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="6">No pending users found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

    </div>
</main>

<style>
/* Inherits styles from admin_dashboard.html for .admin-panel */

.back-link {
    display: inline-block;
    margin-bottom: 1.5rem;
    color: #555;
    text-decoration: none;
}
.back-link:hover {
    text-decoration: underline;
}

.pending-users-info {
    background-color: #f8f9fa;
    border-left: 4px solid #17a2b8;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: 0 4px 4px 0;
}

.user-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.user-table th, .user-table td {
    border: 1px solid #ddd;
    padding: 0.8rem 1rem;
    text-align: left;
    vertical-align: middle;
}

.user-table th {
    background-color: #f8f8f8;
    font-weight: 600;
    color: #333;
}

.user-table tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

.user-table tbody tr:hover {
    background-color: #f5f5f5;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.btn-approve {
    background: #28a745;
    color: white;
}

.btn-reject {
    background: #dc3545;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}

.flash-message {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
}

.flash-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.flash-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.flash-message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}
</style>
{% endblock %}
