{% extends "base.html" %}

{% block title %}Admin - Quiz Attempts{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-modern.css') }}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- Modern Header -->
    <div class="admin-header">
        <div>
            <h1><i class="fas fa-chart-line"></i> Quiz Attempts Management</h1>
            <div class="subtitle">Monitor and analyze all quiz attempts across the system</div>
        </div>
        <div class="header-actions">
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('admin_statistics') }}" class="btn btn-info">
                <i class="fas fa-chart-pie"></i> View Statistics
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="data-card">
        <div class="card-header">
            <h3><i class="fas fa-filter"></i> Filter Quiz Attempts</h3>
            <div class="header-actions">
                <span class="badge badge-info">{{ attempts|length }} Results</span>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" class="filter-form">
                <div class="filter-grid">
                        <!-- Time Filter -->
                        <div class="filter-group">
                            <label for="time_filter">Time Period:</label>
                            <select name="time_filter" id="time_filter">
                                <option value="all" {% if current_time_filter == 'all' %}selected{% endif %}>All Time</option>
                                <option value="today" {% if current_time_filter == 'today' %}selected{% endif %}>Today</option>
                                <option value="week" {% if current_time_filter == 'week' %}selected{% endif %}>Last 7 Days</option>
                                <option value="month" {% if current_time_filter == 'month' %}selected{% endif %}>Last 30 Days</option>
                                <option value="quarter" {% if current_time_filter == 'quarter' %}selected{% endif %}>Last 90 Days</option>
                            </select>
                        </div>

                        <!-- Student Filter -->
                        <div class="filter-group">
                            <label for="student_filter">Student:</label>
                            <select name="student_filter" id="student_filter">
                                <option value="all" {% if current_student_filter == 'all' %}selected{% endif %}>All Students</option>
                                {% for student in students %}
                                    <option value="{{ student.id }}" {% if current_student_filter == student.id|string %}selected{% endif %}>
                                        {{ student.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Quiz Filter -->
                        <div class="filter-group">
                            <label for="quiz_filter">Quiz:</label>
                            <select name="quiz_filter" id="quiz_filter">
                                <option value="all" {% if current_quiz_filter == 'all' %}selected{% endif %}>All Quizzes</option>
                                {% for quiz in quizzes %}
                                    <option value="{{ quiz.id }}" {% if current_quiz_filter == quiz.id|string %}selected{% endif %}>
                                        {{ quiz.title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Score Filter -->
                        <div class="filter-group">
                            <label for="score_filter">Score Range:</label>
                            <select name="score_filter" id="score_filter">
                                <option value="all" {% if current_score_filter == 'all' %}selected{% endif %}>All Scores</option>
                                <option value="excellent" {% if current_score_filter == 'excellent' %}selected{% endif %}>Excellent (90-100%)</option>
                                <option value="good" {% if current_score_filter == 'good' %}selected{% endif %}>Good (80-89%)</option>
                                <option value="average" {% if current_score_filter == 'average' %}selected{% endif %}>Average (70-79%)</option>
                                <option value="below_average" {% if current_score_filter == 'below_average' %}selected{% endif %}>Below Average (60-69%)</option>
                                <option value="poor" {% if current_score_filter == 'poor' %}selected{% endif %}>Poor (Below 60%)</option>
                            </select>
                        </div>
                    </div>

                <div class="filter-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                    <a href="{{ url_for('admin_quiz_attempts') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Results Section -->
    <div class="data-card">
        <div class="card-header">
            <h3><i class="fas fa-table"></i> Quiz Attempts Results</h3>
            <div class="header-actions">
                <span class="badge badge-success">{{ attempts|length }} Total Results</span>
                {% if attempts %}
                    <button onclick="exportToCSV()" class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> Export CSV
                    </button>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            {% if attempts %}
                <div class="table-container">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar"></i> Date & Time</th>
                                <th><i class="fas fa-user-graduate"></i> Student</th>
                                <th><i class="fas fa-clipboard-list"></i> Quiz</th>
                                <th><i class="fas fa-code-branch"></i> Version</th>
                                <th><i class="fas fa-chalkboard-teacher"></i> Teacher</th>
                                <th><i class="fas fa-percentage"></i> Score</th>
                                <th><i class="fas fa-medal"></i> Grade</th>
                                <th><i class="fas fa-layer-group"></i> Difficulty</th>
                                <th><i class="fas fa-info-circle"></i> Status</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                            <tbody>
                                {% for attempt in attempts %}
                                <tr>
                                    <td>{{ attempt.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ attempt.student.name }}</td>
                                    <td>{{ attempt.quiz.title }}</td>
                                    <td>
                                        <span class="version-badge">v{{ attempt.quiz_version }}</span>
                                        {% if attempt.quiz.original_quiz_id %}
                                            <span class="revision-indicator">(Rev)</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ attempt.quiz.teacher.name }}</td>
                                    <td>
                                        <span class="score-badge score-{{ 'excellent' if attempt.score >= 90 else 'good' if attempt.score >= 80 else 'average' if attempt.score >= 70 else 'below-average' if attempt.score >= 60 else 'poor' }}">
                                            {{ "%.1f"|format(attempt.score) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="grade-badge grade-{{ attempt.grade|lower }}">{{ attempt.grade }}</span>
                                    </td>
                                    <td>
                                        <span class="difficulty-badge difficulty-{{ attempt.quiz.difficulty|lower }}">
                                            {{ attempt.quiz.difficulty|capitalize }}
                                        </span>
                                    </td>
                                <td>
                                    {% if attempt.is_locked %}
                                        <span class="status-badge status-locked">
                                            <i class="fas fa-lock"></i> Locked
                                        </span>
                                    {% else %}
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check"></i> Active
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ url_for('view_past_result', attempt_id=attempt.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="no-data-modern">
                    <div class="no-data-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h4>No Quiz Attempts Found</h4>
                    <p>No quiz attempts match the selected filters.</p>
                    <p>Try adjusting your filter criteria or <a href="{{ url_for('admin_quiz_attempts') }}" class="btn btn-outline btn-sm">clear all filters</a>.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% block styles %}
{{ super() }}
<style>
.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.admin-header h1 {
    color: #2c3e50;
    margin: 0;
}

.filter-card, .results-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.card-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
}

.card-header h2 {
    margin: 0;
    color: #495057;
    font-size: 1.25rem;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.export-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.card-body {
    padding: 1.5rem;
}

.filter-form {
    margin: 0;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.filter-group select {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
}

.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #117a8b;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.score-badge, .grade-badge, .difficulty-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.score-excellent { background: #d4edda; color: #155724; }
.score-good { background: #d1ecf1; color: #0c5460; }
.score-average { background: #fff3cd; color: #856404; }
.score-below-average { background: #f8d7da; color: #721c24; }
.score-poor { background: #f5c6cb; color: #721c24; }

.grade-a { background: #d4edda; color: #155724; }
.grade-b { background: #d1ecf1; color: #0c5460; }
.grade-c { background: #fff3cd; color: #856404; }
.grade-d { background: #f8d7da; color: #721c24; }
.grade-f { background: #f5c6cb; color: #721c24; }

.difficulty-easy { background: #d4edda; color: #155724; }
.difficulty-medium { background: #fff3cd; color: #856404; }
.difficulty-hard { background: #f8d7da; color: #721c24; }

.no-results {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.no-results p {
    margin: 0.5rem 0;
}

.no-results a {
    color: #007bff;
    text-decoration: none;
}

.no-results a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .admin-container {
        padding: 1rem;
    }
    
    .admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        flex-direction: column;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
}

/* Version and Status Styling */
.version-badge {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: bold;
}

.revision-indicator {
    background: #17a2b8;
    color: white;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 0.7em;
    margin-left: 3px;
}

.status-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: bold;
}

.status-badge.locked {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function exportToCSV() {
    // Get table data
    const table = document.querySelector('.attempts-table');
    const rows = table.querySelectorAll('tr');

    let csvContent = '';

    // Process each row
    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the Actions column (last column)
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                // Clean up the text and escape quotes
                cellText = cellText.replace(/"/g, '""');
                rowData.push(`"${cellText}"`);
            }
        });

        csvContent += rowData.join(',') + '\n';
    });

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `quiz_attempts_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Update table class for modern styling
document.addEventListener('DOMContentLoaded', function() {
    const table = document.querySelector('.attempts-table');
    if (table) {
        table.className = 'modern-table';
    }
});
</script>
{% endblock %}
{% endblock %}
