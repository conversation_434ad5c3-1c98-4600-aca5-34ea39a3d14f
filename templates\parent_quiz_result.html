{% extends "base.html" %}

{% block title %}{{ child.name }}'s Quiz Result - {{ quiz.title }}{% endblock %}

{% block content %}
<div class="container quiz-result-container">
    <div class="result-header">
        <h1>{{ child.name }}'s Quiz Result</h1>
        <a href="{{ url_for('parent_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
    </div>

    <!-- Quiz Information -->
    <div class="quiz-info-card">
        <h2>{{ quiz.title }}</h2>
        {% if quiz.description %}
            <p class="quiz-description">{{ quiz.description }}</p>
        {% endif %}
        
        <div class="quiz-details">
            <div class="detail-item">
                <span class="label">Student:</span>
                <span class="value">{{ child.name }}</span>
            </div>
            <div class="detail-item">
                <span class="label">Date Taken:</span>
                <span class="value">{{ attempt.submitted_at.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
            <div class="detail-item">
                <span class="label">Time Taken:</span>
                <span class="value">{{ time_taken }}</span>
            </div>
            <div class="detail-item">
                <span class="label">Difficulty:</span>
                <span class="value difficulty-{{ quiz.difficulty }}">{{ quiz.difficulty|title }}</span>
            </div>
        </div>
    </div>

    <!-- Score Summary -->
    <div class="score-summary">
        <div class="score-card main-score">
            <h3>Final Score</h3>
            <div class="score-value">{{ "%.1f"|format(attempt.score) }}%</div>
            <div class="grade-badge grade-{{ grade|lower }}">{{ grade }}</div>
        </div>
        
        <div class="score-breakdown">
            <div class="breakdown-item correct">
                <span class="count">{{ correct_count }}</span>
                <span class="label">Correct</span>
            </div>
            <div class="breakdown-item incorrect">
                <span class="count">{{ incorrect_count }}</span>
                <span class="label">Incorrect</span>
            </div>
            <div class="breakdown-item omitted">
                <span class="count">{{ omitted_count }}</span>
                <span class="label">Omitted</span>
            </div>
            <div class="breakdown-item total">
                <span class="count">{{ total_questions }}</span>
                <span class="label">Total</span>
            </div>
        </div>
    </div>

    <!-- Questions and Answers -->
    <div class="questions-section">
        <h2>Question Details</h2>
        {% for question, answer in questions_with_answers %}
        <div class="question-result-card">
            <div class="question-header">
                <span class="question-number">Question {{ loop.index }}</span>
                <span class="question-marks">({{ question.marks }} marks)</span>
                {% if hasattr(answer, 'is_omitted') and answer.is_omitted %}
                    <span class="status-badge omitted">Omitted</span>
                {% elif answer.is_correct %}
                    <span class="status-badge correct">Correct</span>
                {% else %}
                    <span class="status-badge incorrect">Incorrect</span>
                {% endif %}
            </div>
            
            <div class="question-text">{{ question.question_text }}</div>
            
            {% if question.question_type == 'mcq' %}
                <div class="options-list">
                    {% set options = [question.option1, question.option2, question.option3, question.option4] %}
                    {% for i in range(options|length) %}
                        {% if options[i] %}
                            <div class="option-item 
                                {% if answer.selected_answer == (i+1)|string %}selected{% endif %}
                                {% if question.correct_answer == (i+1)|string %}correct-answer{% endif %}">
                                <span class="option-letter">{{ ['A', 'B', 'C', 'D'][i] }}.</span>
                                <span class="option-text">{{ options[i] }}</span>
                                {% if answer.selected_answer == (i+1)|string %}
                                    <span class="selection-indicator">Selected</span>
                                {% endif %}
                                {% if question.correct_answer == (i+1)|string %}
                                    <span class="correct-indicator">Correct Answer</span>
                                {% endif %}
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% elif question.question_type == 'true_false' %}
                <div class="options-list">
                    <div class="option-item 
                        {% if answer.selected_answer|lower == 'true' %}selected{% endif %}
                        {% if question.correct_answer|lower == 'true' %}correct-answer{% endif %}">
                        <span class="option-letter">A.</span>
                        <span class="option-text">True</span>
                        {% if answer.selected_answer|lower == 'true' %}
                            <span class="selection-indicator">Selected</span>
                        {% endif %}
                        {% if question.correct_answer|lower == 'true' %}
                            <span class="correct-indicator">Correct Answer</span>
                        {% endif %}
                    </div>
                    <div class="option-item 
                        {% if answer.selected_answer|lower == 'false' %}selected{% endif %}
                        {% if question.correct_answer|lower == 'false' %}correct-answer{% endif %}">
                        <span class="option-letter">B.</span>
                        <span class="option-text">False</span>
                        {% if answer.selected_answer|lower == 'false' %}
                            <span class="selection-indicator">Selected</span>
                        {% endif %}
                        {% if question.correct_answer|lower == 'false' %}
                            <span class="correct-indicator">Correct Answer</span>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>

    <!-- Parent Comments Section -->
    <div class="comments-section">
        <h2>Parent Comments & Feedback</h2>
        
        <!-- Add New Comment Form -->
        <div class="add-comment-card">
            <h3>Leave a Comment</h3>
            <p class="comment-description">Share your thoughts about {{ child.name }}'s performance or ask questions about this quiz. Your comments will be visible to the teacher.</p>
            
            <form method="post" class="comment-form">
                <div class="form-group">
                    <label for="comment_text">Your Comment:</label>
                    <textarea id="comment_text" name="comment_text" rows="4" maxlength="1000" 
                              placeholder="Enter your comment or feedback about this quiz attempt..." required></textarea>
                    <div class="character-count">
                        <span id="char-count">0</span>/1000 characters
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Submit Comment</button>
            </form>
        </div>

        <!-- Existing Comments -->
        {% if existing_comments %}
        <div class="existing-comments">
            <h3>Previous Comments</h3>
            {% for comment in existing_comments %}
            <div class="comment-card">
                <div class="comment-header">
                    <span class="comment-author">{{ comment.parent.name }}</span>
                    <span class="comment-date">{{ comment.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                </div>
                <div class="comment-text">{{ comment.comment_text }}</div>
                
                {% if comment.teacher_response %}
                <div class="teacher-response">
                    <div class="response-header">
                        <span class="response-author">Teacher Response - {{ comment.teacher.name }}</span>
                        <span class="response-date">{{ comment.teacher_response_timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                    </div>
                    <div class="response-text">{{ comment.teacher_response }}</div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-comments">
            <p>No comments have been added yet. Be the first to leave feedback!</p>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Character counter for comment textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('comment_text');
    const charCount = document.getElementById('char-count');
    
    if (textarea && charCount) {
        textarea.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            if (count > 900) {
                charCount.style.color = '#dc3545';
            } else if (count > 800) {
                charCount.style.color = '#ffc107';
            } else {
                charCount.style.color = '#6c757d';
            }
        });
    }
});
</script>

<style>
/* Quiz Result Styles */
.quiz-result-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.result-header h1 {
    margin: 0;
    color: #343a40;
}

.quiz-info-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quiz-info-card h2 {
    margin: 0 0 1rem 0;
    color: #007bff;
}

.quiz-description {
    color: #6c757d;
    margin-bottom: 1rem;
}

.quiz-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
}

.detail-item .label {
    font-weight: 600;
    color: #495057;
}

.detail-item .value {
    color: #6c757d;
}

.difficulty-easy { color: #28a745; }
.difficulty-medium { color: #ffc107; }
.difficulty-hard { color: #dc3545; }

/* Score Summary Styles */
.score-summary {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.score-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.score-card h3 {
    margin: 0 0 1rem 0;
    color: #495057;
}

.score-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.grade-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1.1rem;
}

.grade-a { background: #d4edda; color: #155724; }
.grade-b { background: #cce7ff; color: #004085; }
.grade-c { background: #fff3cd; color: #856404; }
.grade-d { background: #f8d7da; color: #721c24; }
.grade-f { background: #f8d7da; color: #721c24; }

.score-breakdown {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.breakdown-item {
    text-align: center;
    padding: 1rem;
    border-radius: 6px;
}

.breakdown-item.correct { background: #d4edda; }
.breakdown-item.incorrect { background: #f8d7da; }
.breakdown-item.omitted { background: #e2e3e5; }
.breakdown-item.total { background: #cce7ff; }

.breakdown-item .count {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.breakdown-item .label {
    font-size: 0.9rem;
    color: #495057;
}

/* Questions Section */
.questions-section {
    margin-bottom: 2rem;
}

.questions-section h2 {
    margin-bottom: 1.5rem;
    color: #343a40;
}

.question-result-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.question-number {
    font-weight: bold;
    color: #007bff;
}

.question-marks {
    color: #6c757d;
    font-size: 0.9em;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-badge.correct {
    background: #d4edda;
    color: #155724;
}

.status-badge.incorrect {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.omitted {
    background: #e2e3e5;
    color: #383d41;
}

.question-text {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #495057;
}

.options-list {
    display: grid;
    gap: 0.5rem;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    gap: 0.75rem;
}

.option-item.selected {
    background: #e7f3ff;
    border-color: #007bff;
}

.option-item.correct-answer {
    background: #d4edda;
    border-color: #28a745;
}

.option-item.selected.correct-answer {
    background: #d1ecf1;
    border-color: #17a2b8;
}

.option-letter {
    font-weight: bold;
    color: #495057;
    min-width: 20px;
}

.option-text {
    flex-grow: 1;
}

.selection-indicator {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.correct-indicator {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* Comments Section */
.comments-section {
    margin-top: 3rem;
}

.comments-section h2 {
    margin-bottom: 1.5rem;
    color: #343a40;
}

.add-comment-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.add-comment-card h3 {
    margin: 0 0 0.5rem 0;
    color: #007bff;
}

.comment-description {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.comment-form .form-group {
    margin-bottom: 1rem;
}

.comment-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.comment-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
}

.comment-form textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.character-count {
    text-align: right;
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.existing-comments h3 {
    margin-bottom: 1rem;
    color: #495057;
}

.comment-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.comment-author {
    font-weight: 600;
    color: #495057;
}

.comment-date {
    color: #6c757d;
    font-size: 0.9rem;
}

.comment-text {
    color: #495057;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.teacher-response {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
    border-left: 4px solid #28a745;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.response-author {
    font-weight: 600;
    color: #28a745;
    font-size: 0.9rem;
}

.response-date {
    color: #6c757d;
    font-size: 0.85rem;
}

.response-text {
    color: #495057;
    line-height: 1.5;
}

.no-comments {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.no-comments p {
    color: #6c757d;
    margin: 0;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .score-summary {
        grid-template-columns: 1fr;
    }
    
    .score-breakdown {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quiz-details {
        grid-template-columns: 1fr;
    }
    
    .result-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .comment-header,
    .response-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>
{% endblock %}
