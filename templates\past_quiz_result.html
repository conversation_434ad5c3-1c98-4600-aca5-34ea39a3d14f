{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="quiz-result-header">
        <h1>{{ quiz.title }}</h1>
        <div class="result-summary">
            <div class="score-box">
                <h2>Your Score</h2>
                <div class="score">{{ "%.2f"|format(attempt.score) }}%</div>
                <div class="grade">Grade: {{ grade }}</div>
            </div>
            <div class="stats-box">
                <div class="stat">
                    <span class="label">Correct Answers:</span>
                    <span class="value">{{ correct_count }}/{{ total_questions }}</span>
                </div>
                <div class="stat">
                    <span class="label">Incorrect Answers:</span>
                    <span class="value">{{ incorrect_count }}/{{ total_questions }}</span>
                </div>
                <div class="stat">
                    <span class="label">Omitted Questions:</span>
                    <span class="value">{{ omitted_count }}/{{ total_questions }}</span>
                </div>
                <div class="stat">
                    <span class="label">Time Taken:</span>
                    <span class="value">{{ time_taken }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="questions-review">
        <h2>Question Review</h2>
        {% for question, answer in questions_with_answers %}
        <div class="question-card {% if answer.is_correct %}correct{% elif answer.is_omitted|default(false) %}omitted{% elif question.question_text == '[This question has been removed]' %}removed{% else %}incorrect{% endif %}">
            <div class="question-header">
                <span class="question-number">Question {{ loop.index }}</span>
                <span class="question-status">
                    {% if answer.is_correct %}
                    ✓ Correct
                    {% elif answer.is_omitted|default(false) %}
                    ⚠ Omitted
                    {% elif question.question_text == '[This question has been removed]' %}
                    ℹ Modified Quiz
                    {% else %}
                    ✗ Incorrect
                    {% endif %}
                </span>
            </div>
            <div class="question-text">{{ question.question_text }}</div>
            <div class="answer-section">
                <div class="your-answer">
                    <strong>Your Answer:</strong>
                    {% if question.question_type == 'mcq' %}
                        {% if answer.selected_answer == '1' %}
                            {{ question.option1 }}
                        {% elif answer.selected_answer == '2' %}
                            {{ question.option2 }}
                        {% elif answer.selected_answer == '3' %}
                            {{ question.option3 }}
                        {% elif answer.selected_answer == '4' %}
                            {{ question.option4 }}
                        {% else %}
                            Not answered
                        {% endif %}
                    {% else %}
                        {{ answer.selected_answer }}
                    {% endif %}
                </div>
                {% if not answer.is_correct %}
                <div class="correct-answer">
                    <strong>Correct Answer:</strong>
                    {% if question.question_type == 'mcq' %}
                        {% if question.correct_answer == '1' %}
                            {{ question.option1 }}
                        {% elif question.correct_answer == '2' %}
                            {{ question.option2 }}
                        {% elif question.correct_answer == '3' %}
                            {{ question.option3 }}
                        {% elif question.correct_answer == '4' %}
                            {{ question.option4 }}
                        {% endif %}
                    {% else %}
                        {{ question.correct_answer }}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Parent Comments Section (Only visible to teachers) -->
    {% if is_teacher_view and parent_comments %}
    <div class="parent-comments-section">
        <h2>Parent Comments</h2>
        {% for comment in parent_comments %}
        <div class="parent-comment-card">
            <div class="comment-header">
                <div class="comment-info">
                    <span class="parent-name">{{ comment.parent.name }}</span>
                    <span class="comment-date">{{ comment.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                </div>
                <div class="comment-actions">
                    {% if not comment.teacher_response %}
                        <span class="status-badge pending">Pending Response</span>
                        <a href="{{ url_for('teacher_respond_to_comment', comment_id=comment.id) }}" class="btn btn-primary btn-sm">Respond</a>
                    {% else %}
                        <span class="status-badge responded">Responded</span>
                        <a href="{{ url_for('teacher_respond_to_comment', comment_id=comment.id) }}" class="btn btn-secondary btn-sm">View Response</a>
                    {% endif %}
                </div>
            </div>
            <div class="comment-text">{{ comment.comment_text }}</div>

            {% if comment.teacher_response %}
            <div class="teacher-response">
                <div class="response-header">
                    <span class="response-label">Your Response:</span>
                    <span class="response-date">{{ comment.teacher_response_timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                </div>
                <div class="response-text">{{ comment.teacher_response }}</div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Teacher Feedback Section (Visible to students and parents) -->
    {% if teacher_feedback and not is_teacher_view %}
    <div class="teacher-feedback-section">
        <h2>Teacher Feedback</h2>
        <div class="feedback-card">
            <div class="feedback-header">
                <span class="feedback-teacher">{{ teacher_feedback.teacher.name }}</span>
                <span class="feedback-date">{{ teacher_feedback.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
            <div class="feedback-text">{{ teacher_feedback.comment_text }}</div>
        </div>
    </div>
    {% endif %}

    <div class="back-button">
        <a href="{{ url_for('student_dashboard') }}" class="btn">Back to Dashboard</a>
    </div>
</div>

<style>
.quiz-result-header {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.result-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.score-box {
    text-align: center;
}

.score {
    font-size: 3rem;
    font-weight: bold;
    color: #4CAF50;
}

.grade {
    font-size: 1.5rem;
    color: #666;
}

.stats-box {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat .label {
    display: block;
    color: #666;
    font-size: 0.9rem;
}

.stat .value {
    font-size: 1.2rem;
    font-weight: bold;
}

.questions-review {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.question-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.question-card.correct {
    border-left: 4px solid #4CAF50;
}

.question-card.incorrect {
    border-left: 4px solid #f44336;
}

.question-card.omitted {
    border-left: 4px solid #ff9800;
}

.question-card.removed {
    border-left: 4px solid #2196F3;
    background-color: #f0f8ff;
}

.question-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.question-status {
    font-weight: bold;
}

.question-text {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.answer-section {
    background: #f5f5f5;
    padding: 1rem;
    border-radius: 5px;
}

.correct-answer {
    color: #4CAF50;
    margin-top: 0.5rem;
}

.back-button {
    text-align: center;
    margin-top: 2rem;
}

.btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    background-color: #4CAF50;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #45a049;
}

/* Parent Comments Styles */
.parent-comments-section {
    margin-top: 3rem;
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.parent-comments-section h2 {
    margin: 0 0 1.5rem 0;
    color: #007bff;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.parent-comment-card {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: #f8f9fa;
}

.parent-comment-card:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.comment-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.parent-name {
    font-weight: 600;
    color: #495057;
}

.comment-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.responded {
    background: #d4edda;
    color: #155724;
}

.comment-text {
    color: #495057;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.teacher-response {
    background: #e8f5e8;
    border-radius: 4px;
    padding: 0.75rem;
    border-left: 4px solid #28a745;
}

.response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.response-label {
    font-weight: 600;
    color: #28a745;
    font-size: 0.9rem;
}

.response-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.response-text {
    color: #495057;
    line-height: 1.5;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .comment-actions {
        align-self: stretch;
        justify-content: space-between;
    }

    .response-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Teacher Feedback Section */
.teacher-feedback-section {
    margin-top: 3rem;
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.teacher-feedback-section h2 {
    margin: 0 0 1.5rem 0;
    color: #28a745;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.feedback-card {
    background: #e8f5e8;
    border-radius: 6px;
    padding: 1rem;
    border-left: 4px solid #28a745;
}

.feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.feedback-teacher {
    font-weight: 600;
    color: #28a745;
}

.feedback-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.feedback-text {
    color: #495057;
    line-height: 1.5;
}

/* Responsive adjustments for teacher feedback */
@media (max-width: 768px) {
    .feedback-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>
{% endblock %}