#!/usr/bin/env python3
"""
Test script for enhanced student report system.
This script validates that the enhanced report system works correctly with all features.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, QuizAttempt, Question, User, ReportCardComment, QuizFeedback, can_teacher_access_student, get_student_performance_data
from werkzeug.security import generate_password_hash
import uuid

def test_enhanced_report_system():
    """Test the enhanced student report system"""
    with app.app_context():
        print("Testing Enhanced Student Report System")
        print("=" * 50)
        
        # Create test users
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Report Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
        
        parent = User.query.filter_by(email='<EMAIL>').first()
        if not parent:
            parent = User(
                name='Test Report Parent',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='parent',
                is_verified=True
            )
            db.session.add(parent)
        
        student = User.query.filter_by(email='<EMAIL>').first()
        if not student:
            student = User(
                name='Test Report Student',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                is_verified=True,
                parent_email='<EMAIL>'
            )
            db.session.add(student)
        
        db.session.commit()
        print("✓ Created test users (teacher, parent, student)")
        
        # Test 1: Create quiz and attempts
        print("\nTest 1: Creating quiz and attempts...")
        quiz = Quiz(
            title='Test Quiz for Enhanced Reports',
            description='A test quiz to validate enhanced report system',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False
        )
        db.session.add(quiz)
        db.session.flush()
        
        # Add test questions
        questions = []
        for i in range(3):
            question = Question(
                quiz_id=quiz.id,
                question_text=f'Question {i+1}: What is {i+1} + {i+1}?',
                question_type='mcq',
                option1=f'{i}',
                option2=f'{(i+1)*2}',
                option3=f'{i+3}',
                option4=f'{i+5}',
                correct_answer='2',
                marks=33
            )
            questions.append(question)
            db.session.add(question)
        
        # Create multiple quiz attempts
        attempts = []
        scores = [85.0, 92.0, 78.0]
        for i, score in enumerate(scores):
            attempt = QuizAttempt(
                student_id=student.id,
                quiz_id=quiz.id,
                score=score,
                quiz_version=quiz.version_number,
                is_locked=False
            )
            attempts.append(attempt)
            db.session.add(attempt)
        
        db.session.commit()
        print(f"✓ Created quiz with {len(questions)} questions and {len(attempts)} attempts")
        
        # Test 2: Test teacher access validation
        print("\nTest 2: Testing teacher access validation...")
        
        # Test valid teacher-student relationship
        can_access = can_teacher_access_student(teacher.id, student.id)
        assert can_access, "Teacher should be able to access student who took their quiz"
        print("✓ Valid teacher-student relationship detected")
        
        # Test invalid teacher (create another teacher)
        other_teacher = User(
            name='Other Teacher',
            email=f'other_teacher_{uuid.uuid4().hex[:8]}@jpischool.com',
            password=generate_password_hash('password123'),
            unhashed_password='password123',
            role='teacher',
            is_verified=True
        )
        db.session.add(other_teacher)
        db.session.commit()
        
        cannot_access = can_teacher_access_student(other_teacher.id, student.id)
        assert not cannot_access, "Other teacher should not be able to access student"
        print("✓ Invalid teacher-student relationship rejected")
        
        # Test 3: Test student performance data
        print("\nTest 3: Testing student performance data...")
        performance_data = get_student_performance_data(student.id)
        
        assert performance_data['total_attempts'] == len(attempts), "Should have correct attempt count"
        assert len(performance_data['attempts']) == len(attempts), "Should have all attempts"
        assert performance_data['average_score'] > 0, "Should have valid average score"
        assert len(performance_data['trend_data']['labels']) > 0, "Should have trend data"
        assert len(performance_data['grade_distribution']) == 5, "Should have all grade categories"
        
        print(f"✓ Performance data: {performance_data['total_attempts']} attempts, {performance_data['average_score']:.1f}% average")
        
        # Test 4: Create report card comment
        print("\nTest 4: Creating report card comment...")
        report_comment = ReportCardComment(
            student_id=student.id,
            teacher_id=teacher.id,
            comment_text="Excellent progress this semester! Shows strong understanding of mathematical concepts and consistent improvement."
        )
        db.session.add(report_comment)
        db.session.commit()
        
        print(f"✓ Created report card comment (ID: {report_comment.id})")
        
        # Test 5: Create quiz feedback
        print("\nTest 5: Creating quiz feedback...")
        quiz_feedbacks = []
        feedback_texts = [
            "Good work on this quiz! Focus on reviewing basic arithmetic.",
            "Excellent performance! Your problem-solving skills are improving.",
            "Keep practicing! Pay attention to reading questions carefully."
        ]
        
        for i, attempt in enumerate(attempts):
            feedback = QuizFeedback(
                attempt_id=attempt.id,
                teacher_id=teacher.id,
                comment_text=feedback_texts[i]
            )
            quiz_feedbacks.append(feedback)
            db.session.add(feedback)
        
        db.session.commit()
        print(f"✓ Created {len(quiz_feedbacks)} quiz feedback entries")
        
        # Test 6: Verify comment relationships
        print("\nTest 6: Verifying comment relationships...")
        
        # Test report card comment relationships
        assert report_comment.student.id == student.id, "Report comment should link to correct student"
        assert report_comment.teacher.id == teacher.id, "Report comment should link to correct teacher"
        print("✓ Report card comment relationships verified")
        
        # Test quiz feedback relationships
        for i, feedback in enumerate(quiz_feedbacks):
            assert feedback.attempt.id == attempts[i].id, "Feedback should link to correct attempt"
            assert feedback.teacher.id == teacher.id, "Feedback should link to correct teacher"
            assert feedback.attempt.student_id == student.id, "Feedback attempt should belong to correct student"
        print("✓ Quiz feedback relationships verified")
        
        # Test 7: Test comment queries
        print("\nTest 7: Testing comment queries...")
        
        # Query report comments by student
        student_comments = ReportCardComment.query.filter_by(student_id=student.id).all()
        assert len(student_comments) == 1, "Should find one report comment for student"
        assert student_comments[0].id == report_comment.id, "Should find correct comment"
        print("✓ Query report comments by student works")
        
        # Query quiz feedback by attempt
        for i, attempt in enumerate(attempts):
            attempt_feedback = QuizFeedback.query.filter_by(attempt_id=attempt.id).all()
            assert len(attempt_feedback) == 1, f"Should find one feedback for attempt {i+1}"
            assert attempt_feedback[0].comment_text == feedback_texts[i], "Should find correct feedback text"
        print("✓ Query quiz feedback by attempt works")
        
        # Query feedback by teacher
        teacher_feedback = QuizFeedback.query.filter_by(teacher_id=teacher.id).all()
        assert len(teacher_feedback) == len(attempts), "Should find all feedback by teacher"
        print("✓ Query feedback by teacher works")
        
        # Test 8: Test data integrity
        print("\nTest 8: Testing data integrity...")
        
        # Test comment text validation
        assert len(report_comment.comment_text) > 0, "Report comment should have text"
        assert report_comment.timestamp is not None, "Report comment should have timestamp"
        print("✓ Report comment data integrity verified")
        
        # Test feedback data integrity
        for feedback in quiz_feedbacks:
            assert len(feedback.comment_text) > 0, "Quiz feedback should have text"
            assert feedback.timestamp is not None, "Quiz feedback should have timestamp"
        print("✓ Quiz feedback data integrity verified")
        
        # Test 9: Test update functionality
        print("\nTest 9: Testing update functionality...")
        
        # Update report card comment
        original_text = report_comment.comment_text
        report_comment.comment_text = "Updated: " + original_text
        db.session.commit()
        
        updated_comment = ReportCardComment.query.get(report_comment.id)
        assert updated_comment.comment_text.startswith("Updated:"), "Report comment should be updated"
        print("✓ Report card comment update works")
        
        # Update quiz feedback
        original_feedback = quiz_feedbacks[0].comment_text
        quiz_feedbacks[0].comment_text = "Updated: " + original_feedback
        db.session.commit()
        
        updated_feedback = QuizFeedback.query.get(quiz_feedbacks[0].id)
        assert updated_feedback.comment_text.startswith("Updated:"), "Quiz feedback should be updated"
        print("✓ Quiz feedback update works")
        
        # Test 10: Test role-based access scenarios
        print("\nTest 10: Testing role-based access scenarios...")
        
        # Test that teacher can access their student's data
        teacher_comments = ReportCardComment.query.filter_by(teacher_id=teacher.id).all()
        assert len(teacher_comments) >= 1, "Teacher should see their own comments"
        print("✓ Teacher can access their own comments")
        
        # Test that student's feedback is accessible
        student_feedback = QuizFeedback.query.join(QuizAttempt)\
            .filter(QuizAttempt.student_id == student.id).all()
        assert len(student_feedback) >= 1, "Student should have accessible feedback"
        print("✓ Student feedback is accessible")
        
        # Cleanup
        print("\nCleaning up test data...")
        try:
            # Delete feedback first (foreign key constraint)
            QuizFeedback.query.filter_by(teacher_id=teacher.id).delete()
            
            # Delete report comments
            ReportCardComment.query.filter_by(student_id=student.id).delete()
            
            # Delete attempts
            for attempt in attempts:
                db.session.delete(attempt)
            
            # Delete questions and quiz
            for question in questions:
                db.session.delete(question)
            db.session.delete(quiz)
            
            # Delete test users
            db.session.delete(student)
            db.session.delete(parent)
            db.session.delete(other_teacher)
            db.session.delete(teacher)
            
            db.session.commit()
            print("✓ Test data cleaned up successfully")
        except Exception as e:
            print(f"Warning: Could not clean up all test data: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All enhanced report system tests passed!")
        print("\nSummary:")
        print("- Report card comment creation and management works")
        print("- Quiz feedback system functions correctly")
        print("- Teacher-student access validation works")
        print("- Student performance data calculation works")
        print("- Database relationships and queries work correctly")
        print("- Update functionality works for both comment types")
        print("- Role-based access control functions properly")
        print("- Data integrity and foreign key constraints work")
        
        return True

if __name__ == "__main__":
    try:
        success = test_enhanced_report_system()
        if success:
            print("\n✅ Enhanced report system test completed successfully!")
        else:
            print("\n❌ Enhanced report system test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
