#!/usr/bin/env python3
"""
Test script for report card export functionality.
This script validates that the report card export features work correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, QuizAttempt, Question, User, ReportCardComment, QuizFeedback, can_export_report_card, get_student_report_data, get_grade_for_score
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta

def test_report_card_export():
    """Test the report card export functionality"""
    with app.app_context():
        print("Testing Report Card Export Functionality")
        print("=" * 50)
        
        # Create test users
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Report Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
        
        student = User.query.filter_by(email='<EMAIL>').first()
        if not student:
            student = User(
                name='Test Report Student',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                parent_email='<EMAIL>',
                is_verified=True
            )
            db.session.add(student)
        
        parent = User.query.filter_by(email='<EMAIL>').first()
        if not parent:
            parent = User(
                name='Test Report Parent',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='parent',
                is_verified=True
            )
            db.session.add(parent)
        
        admin = User.query.filter_by(email='<EMAIL>').first()
        if not admin:
            admin = User(
                name='Test Report Admin',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='admin',
                is_verified=True
            )
            db.session.add(admin)
        
        db.session.commit()
        print("✓ Created test users (teacher, student, parent, admin)")
        
        # Create test quiz
        test_quiz = Quiz(
            title='Test Report Quiz',
            description='A quiz for testing report card export',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium'
        )
        db.session.add(test_quiz)
        db.session.flush()
        
        # Add test questions
        question = Question(
            quiz_id=test_quiz.id,
            question_text='What is 2 + 2?',
            question_type='mcq',
            option1='3',
            option2='4',
            option3='5',
            option4='6',
            correct_answer='2',
            marks=100
        )
        db.session.add(question)
        db.session.commit()
        print("✓ Created test quiz with question")
        
        # Create test quiz attempts with different scores
        attempts = []
        scores = [95, 85, 75, 65, 55]  # Different grades
        
        for i, score in enumerate(scores):
            attempt = QuizAttempt(
                student_id=student.id,
                quiz_id=test_quiz.id,
                score=score,
                submitted_at=datetime.now() - timedelta(days=i*7)  # Weekly intervals
            )
            attempts.append(attempt)
            db.session.add(attempt)
        
        db.session.commit()
        print(f"✓ Created {len(attempts)} test quiz attempts")
        
        # Create teacher's overall comment
        teacher_comment = ReportCardComment(
            student_id=student.id,
            teacher_id=teacher.id,
            comment_text="This student shows excellent progress in mathematics. Strong analytical skills and consistent improvement over time."
        )
        db.session.add(teacher_comment)
        
        # Create quiz-specific feedback
        quiz_feedback = QuizFeedback(
            attempt_id=attempts[0].id,  # Feedback on best attempt
            teacher_id=teacher.id,
            comment_text="Outstanding work! Perfect understanding of basic arithmetic concepts."
        )
        db.session.add(quiz_feedback)
        
        db.session.commit()
        print("✓ Created teacher comments and quiz feedback")
        
        # Test 1: Access control
        print("\nTest 1: Testing access control...")
        
        # Teacher should be able to export student report
        assert can_export_report_card(student, teacher.id, teacher.role) == True, "Teacher should be able to export student report"
        print("✓ Teacher can export student report")
        
        # Parent should be able to export their child's report
        assert can_export_report_card(student, parent.id, parent.role) == True, "Parent should be able to export their child's report"
        print("✓ Parent can export their child's report")
        
        # Admin should be able to export any report
        assert can_export_report_card(student, admin.id, admin.role) == True, "Admin should be able to export any report"
        print("✓ Admin can export any report")
        
        # Student should not be able to export reports
        assert can_export_report_card(student, student.id, student.role) == False, "Student should not be able to export reports"
        print("✓ Student cannot export reports")
        
        # Test 2: Report data generation
        print("\nTest 2: Testing report data generation...")
        
        report_data = get_student_report_data(student.id)
        
        assert report_data['student'].id == student.id, "Report should be for correct student"
        assert len(report_data['attempts']) == 5, "Report should include all attempts"
        assert report_data['average_score'] == 75.0, "Average score should be calculated correctly"
        assert report_data['highest_score'] == 95.0, "Highest score should be identified correctly"
        assert report_data['lowest_score'] == 55.0, "Lowest score should be identified correctly"
        assert report_data['teacher_comment'] is not None, "Teacher comment should be included"
        assert len(report_data['quiz_feedback']) == 1, "Quiz feedback should be included"
        print("✓ Report data generation works correctly")
        
        # Test 3: Grade calculation
        print("\nTest 3: Testing grade calculation...")
        
        assert get_grade_for_score(95, test_quiz) == 'A', "Score 95 should be grade A"
        assert get_grade_for_score(85, test_quiz) == 'B', "Score 85 should be grade B"
        assert get_grade_for_score(75, test_quiz) == 'C', "Score 75 should be grade C"
        assert get_grade_for_score(65, test_quiz) == 'D', "Score 65 should be grade D"
        assert get_grade_for_score(55, test_quiz) == 'F', "Score 55 should be grade F"
        print("✓ Grade calculation works correctly")
        
        # Test 4: Date range filtering
        print("\nTest 4: Testing date range filtering...")
        
        # Test with date range that includes only recent attempts
        start_date = datetime.now() - timedelta(days=14)
        end_date = datetime.now()
        filtered_data = get_student_report_data(student.id, (start_date, end_date))
        
        assert len(filtered_data['attempts']) <= 3, "Date filtering should limit attempts"
        print("✓ Date range filtering works correctly")
        
        # Test 5: Report statistics
        print("\nTest 5: Testing report statistics...")
        
        assert report_data['total_quizzes'] == 5, "Total quiz count should be correct"
        
        # Verify score calculations
        expected_avg = sum(scores) / len(scores)
        assert abs(report_data['average_score'] - expected_avg) < 0.1, "Average calculation should be accurate"
        print("✓ Report statistics are accurate")
        
        # Test 6: File naming safety
        print("\nTest 6: Testing file naming safety...")
        
        # Test with special characters in student name
        special_student = User(
            name='Test Student: "Special" & <Characters>',
            email='<EMAIL>',
            password=generate_password_hash('password123'),
            role='student'
        )
        db.session.add(special_student)
        db.session.commit()
        
        # Test filename generation (simulated)
        import re
        safe_name = re.sub(r'[^\w\s-]', '', special_student.name).strip()
        safe_name = re.sub(r'[-\s]+', '-', safe_name)
        expected_filename = f"report-card-{safe_name}-{special_student.id}.pdf"
        
        assert 'Test-Student-Special-Characters' in expected_filename, "Special characters should be removed from filename"
        print("✓ File name safety works correctly")
        
        # Test 7: Export route simulation
        print("\nTest 7: Testing export route logic...")
        
        with app.test_client() as client:
            # Test without login (should redirect)
            response = client.get(f'/export_report/{student.id}')
            assert response.status_code in [302, 401], "Should require login"
            print("✓ Export routes require authentication")
        
        # Cleanup
        print("\nCleaning up test data...")
        try:
            # Delete feedback and comments first
            db.session.delete(quiz_feedback)
            db.session.delete(teacher_comment)
            
            # Delete attempts
            for attempt in attempts:
                db.session.delete(attempt)
            
            # Delete question and quiz
            db.session.delete(question)
            db.session.delete(test_quiz)
            
            # Delete users
            db.session.delete(special_student)
            db.session.delete(admin)
            db.session.delete(parent)
            db.session.delete(student)
            db.session.delete(teacher)
            
            db.session.commit()
            print("✓ Test data cleaned up successfully")
        except Exception as e:
            print(f"Warning: Could not clean up all test data: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All report card export tests passed!")
        print("\nSummary:")
        print("- Access control works correctly for all user roles")
        print("- Report data generation includes all required information")
        print("- Grade calculation follows quiz thresholds correctly")
        print("- Date range filtering works for custom periods")
        print("- Statistics calculations are accurate")
        print("- File name safety handles special characters")
        print("- Export routes require proper authentication")
        
        return True

if __name__ == "__main__":
    try:
        success = test_report_card_export()
        if success:
            print("\n✅ Report card export test completed successfully!")
        else:
            print("\n❌ Report card export test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
