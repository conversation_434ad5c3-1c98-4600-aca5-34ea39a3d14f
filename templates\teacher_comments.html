{% extends "base.html" %}

{% block title %}Parent Comments{% endblock %}

{% block content %}
<div class="container comments-container">
    <div class="comments-header">
        <h1>Parent Comments & Feedback</h1>
        <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
    </div>

    {% if comments %}
    <div class="comments-summary">
        <div class="summary-card">
            <h3>{{ comments|length }}</h3>
            <p>Total Comments</p>
        </div>
        <div class="summary-card">
            <h3>{{ comments|selectattr('teacher_response')|list|length }}</h3>
            <p>Responded</p>
        </div>
        <div class="summary-card">
            <h3>{{ (comments|length) - (comments|selectattr('teacher_response')|list|length) }}</h3>
            <p>Pending Response</p>
        </div>
    </div>

    <div class="comments-list">
        {% for comment in comments %}
        <div class="comment-item {% if not comment.teacher_response %}pending{% endif %}">
            <div class="comment-header">
                <div class="comment-info">
                    <h3>{{ comment.quiz.title }}</h3>
                    <div class="comment-meta">
                        <span class="student-name">Student: {{ comment.student.name }}</span>
                        <span class="parent-name">Parent: {{ comment.parent.name }}</span>
                        <span class="comment-date">{{ comment.timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                        <span class="quiz-score">Score: {{ "%.1f"|format(comment.attempt.score) }}%</span>
                    </div>
                </div>
                <div class="comment-actions">
                    {% if not comment.teacher_response %}
                        <span class="status-badge pending">Pending Response</span>
                        <a href="{{ url_for('teacher_respond_to_comment', comment_id=comment.id) }}" class="btn btn-primary btn-sm">Respond</a>
                    {% else %}
                        <span class="status-badge responded">Responded</span>
                        <a href="{{ url_for('teacher_respond_to_comment', comment_id=comment.id) }}" class="btn btn-secondary btn-sm">View Response</a>
                    {% endif %}
                </div>
            </div>
            
            <div class="comment-content">
                <div class="parent-comment">
                    <h4>Parent Comment:</h4>
                    <p>{{ comment.comment_text }}</p>
                </div>
                
                {% if comment.teacher_response %}
                <div class="teacher-response">
                    <h4>Your Response:</h4>
                    <p>{{ comment.teacher_response }}</p>
                    <div class="response-meta">
                        <span>Responded on {{ comment.teacher_response_timestamp.strftime('%B %d, %Y at %I:%M %p') }}</span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="no-comments">
        <div class="no-comments-icon">💬</div>
        <h2>No Parent Comments Yet</h2>
        <p>When parents leave comments on their children's quiz attempts, they will appear here.</p>
        <p>Parent comments help facilitate communication between parents and teachers about student performance.</p>
    </div>
    {% endif %}
</div>

<style>
.comments-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.comments-header h1 {
    margin: 0;
    color: #343a40;
}

.comments-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.summary-card h3 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    color: #007bff;
}

.summary-card p {
    margin: 0;
    color: #6c757d;
    font-weight: 600;
}

.comments-list {
    display: grid;
    gap: 1.5rem;
}

.comment-item {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #28a745;
}

.comment-item.pending {
    border-left-color: #ffc107;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.comment-info h3 {
    margin: 0 0 0.5rem 0;
    color: #007bff;
}

.comment-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.comment-meta span {
    display: flex;
    align-items: center;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.responded {
    background: #d4edda;
    color: #155724;
}

.comment-content {
    display: grid;
    gap: 1rem;
}

.parent-comment,
.teacher-response {
    padding: 1rem;
    border-radius: 6px;
}

.parent-comment {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
}

.teacher-response {
    background: #e8f5e8;
    border-left: 4px solid #28a745;
}

.parent-comment h4,
.teacher-response h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #495057;
}

.parent-comment p,
.teacher-response p {
    margin: 0;
    line-height: 1.5;
    color: #495057;
}

.response-meta {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

.no-comments {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.no-comments-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-comments h2 {
    margin: 0 0 1rem 0;
    color: #495057;
}

.no-comments p {
    color: #6c757d;
    margin-bottom: 0.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .comments-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .comment-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .comment-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .comment-actions {
        align-self: stretch;
        justify-content: center;
    }
    
    .comments-summary {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
