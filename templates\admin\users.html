{% extends "base.html" %}

{% block title %}Manage Users{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-modern.css') }}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- Modern Header -->
    <div class="admin-header">
        <div>
            <h1><i class="fas fa-users-cog"></i> User Management</h1>
            <div class="subtitle">Manage users, roles, and permissions</div>
        </div>
        <div class="header-actions">
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('admin_pending_users') }}" class="btn btn-warning">
                <i class="fas fa-user-clock"></i> Pending Users
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }}"></i>
            {{ message }}
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    <!-- Users Table -->
    <div class="data-card">
        <div class="card-header">
            <h3><i class="fas fa-table"></i> All Users</h3>
            <div class="header-actions">
                <span class="badge badge-info">{{ users|length }} Total Users</span>
            </div>
        </div>

        <div class="table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag"></i> ID</th>
                        <th><i class="fas fa-user"></i> Name</th>
                        <th><i class="fas fa-envelope"></i> Email</th>
                        <th><i class="fas fa-user-tag"></i> Role</th>
                        <th><i class="fas fa-users"></i> Parent Email</th>
                        <th><i class="fas fa-check-circle"></i> Status</th>
                        <th><i class="fas fa-cogs"></i> Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td><span class="badge badge-secondary">#{{ user.id }}</span></td>
                        <td>
                            <div class="user-info">
                                <strong>{{ user.name }}</strong>
                            </div>
                        </td>
                        <td>
                            <span class="email-text">{{ user.email }}</span>
                        </td>
                        <td>
                            <span class="badge badge-{{ 'danger' if user.role == 'admin' else 'primary' if user.role == 'teacher' else 'success' if user.role == 'student' else 'warning' }}">
                                <i class="fas fa-{{ 'crown' if user.role == 'admin' else 'chalkboard-teacher' if user.role == 'teacher' else 'user-graduate' if user.role == 'student' else 'users' }}"></i>
                                {{ user.role.title() }}
                            </span>
                        </td>
                        <td>
                            {% if user.parent_email %}
                                <span class="parent-email">{{ user.parent_email }}</span>
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_verified %}
                                <span class="status-badge status-active">
                                    <i class="fas fa-check"></i> Verified
                                </span>
                            {% else %}
                                <span class="status-badge status-pending">
                                    <i class="fas fa-clock"></i> Pending
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{{ url_for('admin_edit_user', user_id=user.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <form action="{{ url_for('admin_delete_user', user_id=user.id) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.');">
                                    <button type="submit" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="no-data-modern">
                                <div class="no-data-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h4>No Users Found</h4>
                                <p>No users are currently registered in the system.</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
/* Inherits styles from admin_dashboard.html for .admin-panel */

.back-link {
    display: inline-block;
    margin-bottom: 1.5rem;
    color: #555;
    text-decoration: none;
}
.back-link:hover {
    text-decoration: underline;
}

.user-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.user-table th, .user-table td {
    border: 1px solid #ddd;
    padding: 0.8rem 1rem;
    text-align: left;
    vertical-align: middle;
}

.user-table th {
    background-color: #f8f8f8;
    font-weight: 600;
    color: #333;
}

.user-table tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

.user-table tbody tr:hover {
    background-color: #f1f1f1;
}

.btn {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9em;
    margin-right: 5px;
    transition: background-color 0.2s ease;
}

.btn-edit {
    background-color: #ffc107; /* Yellow */
    color: #333;
}
.btn-edit:hover {
    background-color: #e0a800;
}

.btn-delete {
    background-color: #dc3545; /* Red */
    color: white;
}
.btn-delete:hover {
    background-color: #c82333;
}

/* Flash Messages (reuse from login/signup if available or add here) */
.flash-message {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    color: white;
}
.flash-message.success { background-color: #28a745; }
.flash-message.error { background-color: #dc3545; }
.flash-message.info { background-color: #17a2b8; }

.status-verified {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #28a745;
    color: white;
    border-radius: 4px;
    font-size: 0.85em;
}

.status-pending {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #ffc107;
    color: #333;
    border-radius: 4px;
    font-size: 0.85em;
}

/* Additional modern styles */
.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons form {
    margin: 0;
}

.email-text {
    font-family: monospace;
    font-size: 0.9rem;
    color: #6c757d;
}

.parent-email {
    font-family: monospace;
    font-size: 0.85rem;
    color: #adb5bd;
}

.text-muted {
    color: #adb5bd;
    font-style: italic;
}

.text-center {
    text-align: center;
}

.user-info strong {
    color: #2c3e50;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}
</style>
{% endblock %}