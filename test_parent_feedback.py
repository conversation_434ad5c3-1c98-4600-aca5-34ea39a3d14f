#!/usr/bin/env python3
"""
Test script for parent feedback system.
This script validates that the parent comment and feedback system works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, QuizAttempt, Question, User, ParentComment, can_parent_comment_on_attempt, get_teacher_for_attempt
from werkzeug.security import generate_password_hash
import uuid

def test_parent_feedback_system():
    """Test the parent feedback system"""
    with app.app_context():
        print("Testing Parent Feedback System")
        print("=" * 50)
        
        # Create test users
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Feedback Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
        
        parent = User.query.filter_by(email='<EMAIL>').first()
        if not parent:
            parent = User(
                name='Test Feedback Parent',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='parent',
                is_verified=True
            )
            db.session.add(parent)
        
        student = User.query.filter_by(email='<EMAIL>').first()
        if not student:
            student = User(
                name='Test Feedback Student',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                is_verified=True,
                parent_email='<EMAIL>'
            )
            db.session.add(student)
        
        db.session.commit()
        print("✓ Created test users (teacher, parent, student)")
        
        # Test 1: Create quiz and attempt
        print("\nTest 1: Creating quiz and attempt...")
        quiz = Quiz(
            title='Test Quiz for Parent Feedback',
            description='A test quiz to validate parent feedback system',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=50,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False
        )
        db.session.add(quiz)
        db.session.flush()
        
        # Add test question
        question = Question(
            quiz_id=quiz.id,
            question_text='What is 2+2?',
            question_type='mcq',
            option1='3',
            option2='4',
            option3='5',
            option4='6',
            correct_answer='2',
            marks=50
        )
        db.session.add(question)
        
        # Create quiz attempt
        attempt = QuizAttempt(
            student_id=student.id,
            quiz_id=quiz.id,
            score=80.0,
            quiz_version=quiz.version_number,
            is_locked=False
        )
        db.session.add(attempt)
        db.session.commit()
        
        print(f"✓ Created quiz and attempt (Score: {attempt.score}%)")
        
        # Test 2: Test parent permission validation
        print("\nTest 2: Testing parent permission validation...")
        
        # Test valid parent-student relationship
        can_comment = can_parent_comment_on_attempt(parent.id, attempt.id)
        assert can_comment, "Parent should be able to comment on their child's attempt"
        print("✓ Valid parent-student relationship detected")
        
        # Test invalid parent (create another parent)
        other_parent = User(
            name='Other Parent',
            email=f'other_parent_{uuid.uuid4().hex[:8]}@jpischool.com',
            password=generate_password_hash('password123'),
            unhashed_password='password123',
            role='parent',
            is_verified=True
        )
        db.session.add(other_parent)
        db.session.commit()
        
        cannot_comment = can_parent_comment_on_attempt(other_parent.id, attempt.id)
        assert not cannot_comment, "Other parent should not be able to comment"
        print("✓ Invalid parent-student relationship rejected")
        
        # Test 3: Test teacher identification
        print("\nTest 3: Testing teacher identification...")
        found_teacher = get_teacher_for_attempt(attempt.id)
        assert found_teacher is not None, "Should find teacher for attempt"
        assert found_teacher.id == teacher.id, "Should find correct teacher"
        print(f"✓ Correct teacher identified: {found_teacher.name}")
        
        # Test 4: Create parent comment
        print("\nTest 4: Creating parent comment...")
        comment = ParentComment(
            attempt_id=attempt.id,
            parent_id=parent.id,
            comment_text="Great job on this quiz! I'm proud of your improvement in math."
        )
        db.session.add(comment)
        db.session.commit()
        
        print(f"✓ Created parent comment (ID: {comment.id})")
        
        # Test 5: Verify comment relationships
        print("\nTest 5: Verifying comment relationships...")
        
        # Test comment-attempt relationship
        assert comment.attempt.id == attempt.id, "Comment should link to correct attempt"
        assert comment.attempt.student_id == student.id, "Attempt should link to correct student"
        print("✓ Comment-attempt relationship verified")
        
        # Test comment-parent relationship
        assert comment.parent.id == parent.id, "Comment should link to correct parent"
        assert comment.parent.role == 'parent', "Comment author should be parent"
        print("✓ Comment-parent relationship verified")
        
        # Test 6: Add teacher response
        print("\nTest 6: Adding teacher response...")
        comment.teacher_response = "Thank you for your feedback! Your child is doing excellent work and shows great improvement."
        comment.teacher_response_timestamp = db.func.now()
        comment.teacher_id = teacher.id
        db.session.commit()
        
        print("✓ Added teacher response")
        
        # Test 7: Verify teacher response relationships
        print("\nTest 7: Verifying teacher response relationships...")
        assert comment.teacher_response is not None, "Should have teacher response"
        assert comment.teacher_id == teacher.id, "Should link to correct teacher"
        assert comment.teacher.role == 'teacher', "Responder should be teacher"
        print("✓ Teacher response relationships verified")
        
        # Test 8: Test comment queries
        print("\nTest 8: Testing comment queries...")
        
        # Query comments by attempt
        attempt_comments = ParentComment.query.filter_by(attempt_id=attempt.id).all()
        assert len(attempt_comments) == 1, "Should find one comment for attempt"
        assert attempt_comments[0].id == comment.id, "Should find correct comment"
        print("✓ Query by attempt works")
        
        # Query comments by parent
        parent_comments = ParentComment.query.filter_by(parent_id=parent.id).all()
        assert len(parent_comments) == 1, "Should find one comment by parent"
        print("✓ Query by parent works")
        
        # Query comments for teacher (via quiz relationship)
        teacher_comments = db.session.query(ParentComment)\
            .join(QuizAttempt, ParentComment.attempt_id == QuizAttempt.id)\
            .join(Quiz, QuizAttempt.quiz_id == Quiz.id)\
            .filter(Quiz.teacher_id == teacher.id)\
            .all()
        assert len(teacher_comments) == 1, "Should find one comment for teacher"
        print("✓ Query for teacher works")
        
        # Test 9: Test multiple comments
        print("\nTest 9: Testing multiple comments...")
        
        # Add second comment
        comment2 = ParentComment(
            attempt_id=attempt.id,
            parent_id=parent.id,
            comment_text="I noticed some areas where improvement is needed. Could we discuss strategies?"
        )
        db.session.add(comment2)
        db.session.commit()
        
        # Verify multiple comments
        all_comments = ParentComment.query.filter_by(attempt_id=attempt.id)\
            .order_by(ParentComment.timestamp.desc()).all()
        assert len(all_comments) == 2, "Should have two comments"
        assert all_comments[0].id == comment2.id, "Newest comment should be first"
        print("✓ Multiple comments handling works")
        
        # Test 10: Test data integrity
        print("\nTest 10: Testing data integrity...")
        
        # Test comment text validation
        assert len(comment.comment_text) > 0, "Comment should have text"
        assert comment.timestamp is not None, "Comment should have timestamp"
        print("✓ Comment data integrity verified")
        
        # Test foreign key relationships
        assert comment.attempt_id == attempt.id, "Foreign key to attempt should be correct"
        assert comment.parent_id == parent.id, "Foreign key to parent should be correct"
        assert comment.teacher_id == teacher.id, "Foreign key to teacher should be correct"
        print("✓ Foreign key relationships verified")
        
        # Cleanup
        print("\nCleaning up test data...")
        try:
            # Delete comments first (foreign key constraint)
            ParentComment.query.filter_by(attempt_id=attempt.id).delete()
            
            # Delete attempt
            db.session.delete(attempt)
            
            # Delete question and quiz
            db.session.delete(question)
            db.session.delete(quiz)
            
            # Delete test users
            db.session.delete(student)
            db.session.delete(parent)
            db.session.delete(other_parent)
            db.session.delete(teacher)
            
            db.session.commit()
            print("✓ Test data cleaned up successfully")
        except Exception as e:
            print(f"Warning: Could not clean up all test data: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All parent feedback system tests passed!")
        print("\nSummary:")
        print("- Parent comment creation and validation works")
        print("- Parent-student relationship validation works")
        print("- Teacher identification and response system works")
        print("- Database relationships and queries work correctly")
        print("- Multiple comments per attempt supported")
        print("- Data integrity and foreign key constraints work")
        print("- Role-based access control functions properly")
        
        return True

if __name__ == "__main__":
    try:
        success = test_parent_feedback_system()
        if success:
            print("\n✅ Parent feedback system test completed successfully!")
        else:
            print("\n❌ Parent feedback system test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
