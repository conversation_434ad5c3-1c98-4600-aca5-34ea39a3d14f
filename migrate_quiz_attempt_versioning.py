#!/usr/bin/env python3
"""
Migration script to add versioning fields to QuizAttempt table.
This script adds is_locked and quiz_version fields to support quiz versioning.
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_quiz_attempt_versioning():
    """Add versioning fields to QuizAttempt table"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure you're running this script from the correct directory.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if versioning columns already exist in quiz_attempt table
        cursor.execute("PRAGMA table_info(quiz_attempt)")
        columns = [column[1] for column in cursor.fetchall()]
        
        versioning_columns = [
            'is_locked',
            'quiz_version'
        ]
        
        missing_columns = [col for col in versioning_columns if col not in columns]
        
        if not missing_columns:
            print("QuizAttempt table already has versioning support. No migration needed.")
            return
        
        print(f"Adding missing columns to quiz_attempt table: {missing_columns}")
        
        # Add missing columns
        if 'is_locked' in missing_columns:
            cursor.execute("""
                ALTER TABLE quiz_attempt ADD COLUMN is_locked BOOLEAN 
                DEFAULT 0 NOT NULL
            """)
            print("✓ Added is_locked column")
        
        if 'quiz_version' in missing_columns:
            cursor.execute("""
                ALTER TABLE quiz_attempt ADD COLUMN quiz_version INTEGER 
                DEFAULT 1 NOT NULL
            """)
            print("✓ Added quiz_version column")
        
        # Update existing quiz attempts with proper version numbers
        print("Updating existing quiz attempts with version information...")
        
        # Get all existing attempts and set their quiz_version based on the quiz they attempted
        cursor.execute("""
            UPDATE quiz_attempt 
            SET quiz_version = (
                SELECT COALESCE(q.version_number, 1) 
                FROM quiz q 
                WHERE q.id = quiz_attempt.quiz_id
            )
            WHERE quiz_version = 1
        """)
        
        # Lock attempts for quizzes that are now locked
        cursor.execute("""
            UPDATE quiz_attempt 
            SET is_locked = 1
            WHERE quiz_id IN (
                SELECT id FROM quiz WHERE is_locked = 1
            )
        """)
        
        conn.commit()
        print("✓ Database migration completed successfully!")
        print("\nQuizAttempt versioning is now enabled:")
        print("- Existing attempts are marked with proper version numbers")
        print("- Attempts for locked quizzes are marked as locked")
        print("- New attempts will automatically track quiz versions")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Starting QuizAttempt versioning migration...")
    print("=" * 50)
    migrate_quiz_attempt_versioning()
    print("=" * 50)
    print("Migration completed!")
