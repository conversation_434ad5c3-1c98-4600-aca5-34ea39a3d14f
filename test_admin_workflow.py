#!/usr/bin/env python3
"""
Test script to simulate the exact admin workflow for user approval/rejection.
This will help identify where the errors are occurring.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, User
from werkzeug.security import generate_password_hash
import uuid

def create_test_user():
    """Create a test user for approval/rejection testing"""
    with app.app_context():
        test_email = f"test_admin_workflow_{uuid.uuid4().hex[:8]}@jpischool.com"
        test_token = f"token_{uuid.uuid4().hex}"
        
        test_user = User(
            name='Test Admin Workflow User',
            email=test_email,
            password=generate_password_hash('password123'),
            unhashed_password='password123',
            role='student',
            is_verified=False,
            verification_token=test_token
        )
        
        try:
            db.session.add(test_user)
            db.session.commit()
            print(f"✓ Created test user: ID={test_user.id}, Email={test_email}, Token={test_token}")
            return test_user.id, test_token
        except Exception as e:
            print(f"❌ Error creating test user: {e}")
            return None, None

def test_admin_verify_user(user_id, token):
    """Test the admin_verify_user function directly"""
    with app.app_context():
        print(f"\nTesting admin_verify_user with user_id={user_id}, token={token}")
        
        try:
            # Get the user
            user = User.query.get(user_id)
            if not user:
                print(f"❌ User with ID {user_id} not found")
                return False
            
            print(f"✓ Found user: {user.name} ({user.email})")
            print(f"  - Current verification status: {user.is_verified}")
            print(f"  - Current token: {user.verification_token}")
            
            # Check if user is already verified
            if user.is_verified:
                print(f"ℹ️ User {user.name} is already verified")
                return True

            # Verify the token matches
            if not user.verification_token or user.verification_token != token:
                print(f"❌ Invalid or expired verification token")
                print(f"  - Expected: {token}")
                print(f"  - Actual: {user.verification_token}")
                return False

            # Verify the user
            user.is_verified = True
            user.verification_token = None
            db.session.commit()
            
            print(f"✓ User {user.name} verified successfully")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error verifying user: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_admin_reject_user(user_id):
    """Test the admin_reject_user function directly"""
    with app.app_context():
        print(f"\nTesting admin_reject_user with user_id={user_id}")
        
        try:
            # Get the user
            user = User.query.get(user_id)
            if not user:
                print(f"❌ User with ID {user_id} not found")
                return False
            
            print(f"✓ Found user: {user.name} ({user.email})")
            
            # Check if user is already verified
            if user.is_verified:
                print(f"❌ Cannot reject user {user.name} - they are already verified")
                return False
            
            user_email = user.email
            user_name = user.name

            # Delete the user
            db.session.delete(user)
            db.session.commit()
            
            print(f"✓ User {user_name} rejected and removed successfully")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error rejecting user: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_pending_users_query():
    """Test the pending users query"""
    with app.app_context():
        print(f"\nTesting pending users query...")
        
        try:
            pending_users = User.query.filter_by(is_verified=False).all()
            print(f"✓ Found {len(pending_users)} pending users:")
            
            for user in pending_users:
                print(f"  - ID: {user.id}, Name: {user.name}, Email: {user.email}, Role: {user.role}")
                print(f"    Token: {user.verification_token}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error querying pending users: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_login_with_unverified_user(email, password):
    """Test login with unverified user"""
    with app.app_context():
        print(f"\nTesting login with unverified user: {email}")
        
        try:
            user = User.query.filter_by(email=email).first()
            if not user:
                print(f"❌ User with email {email} not found")
                return False
            
            from werkzeug.security import check_password_hash
            
            if check_password_hash(user.password, password):
                if not user.is_verified and user.role != 'admin':
                    print(f"✓ Login validation works: User is unverified and login should be blocked")
                    return True
                else:
                    print(f"✓ User is verified, login should be allowed")
                    return True
            else:
                print(f"❌ Password check failed")
                return False
                
        except Exception as e:
            print(f"❌ Error testing login: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    print("Testing Admin Workflow for User Approval/Rejection")
    print("=" * 60)
    
    # Test 1: Create test user
    user_id, token = create_test_user()
    if not user_id:
        print("❌ Failed to create test user")
        return False
    
    # Test 2: Test pending users query
    if not test_pending_users_query():
        print("❌ Pending users query failed")
        return False
    
    # Test 3: Test login with unverified user
    with app.app_context():
        user = User.query.get(user_id)
        if not test_login_with_unverified_user(user.email, 'password123'):
            print("❌ Login test with unverified user failed")
            return False
    
    # Test 4: Test user approval
    if not test_admin_verify_user(user_id, token):
        print("❌ User approval test failed")
        return False
    
    # Test 5: Test login with verified user
    with app.app_context():
        user = User.query.get(user_id)
        if not test_login_with_unverified_user(user.email, 'password123'):
            print("❌ Login test with verified user failed")
            return False
    
    # Test 6: Create another user for rejection test
    reject_user_id, reject_token = create_test_user()
    if not reject_user_id:
        print("❌ Failed to create user for rejection test")
        return False
    
    # Test 7: Test user rejection
    if not test_admin_reject_user(reject_user_id):
        print("❌ User rejection test failed")
        return False
    
    # Clean up approved user
    with app.app_context():
        try:
            user = User.query.get(user_id)
            if user:
                db.session.delete(user)
                db.session.commit()
                print(f"✓ Cleaned up test user {user_id}")
        except Exception as e:
            print(f"Warning: Could not clean up user {user_id}: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 All admin workflow tests passed!")
    print("\nThe admin approval/rejection system is working correctly.")
    print("If you're still experiencing errors in the web interface,")
    print("the issue might be with:")
    print("- Flask-Mail configuration")
    print("- Route URL generation")
    print("- Session management")
    print("- Template rendering")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
