#!/usr/bin/env python3
"""
Migration script to add quiz versioning fields to existing database.
Run this script once to update your database schema.
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """Add versioning fields to the Quiz table"""
    
    # Database path
    db_path = 'instance/quiz.db'
    
    if not os.path.exists(db_path):
        print("Database file not found. Creating new database with versioning support.")
        return
    
    print("Starting database migration for quiz versioning...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if versioning columns already exist
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [column[1] for column in cursor.fetchall()]
        
        versioning_columns = [
            'original_quiz_id',
            'version_number', 
            'is_active',
            'is_locked',
            'locked_at'
        ]
        
        missing_columns = [col for col in versioning_columns if col not in columns]
        
        if not missing_columns:
            print("Database already has versioning support. No migration needed.")
            return
        
        print(f"Adding missing columns: {missing_columns}")
        
        # Add missing columns
        if 'original_quiz_id' in missing_columns:
            cursor.execute("""
                ALTER TABLE quiz ADD COLUMN original_quiz_id INTEGER 
                REFERENCES quiz(id)
            """)
            print("✓ Added original_quiz_id column")
        
        if 'version_number' in missing_columns:
            cursor.execute("""
                ALTER TABLE quiz ADD COLUMN version_number INTEGER DEFAULT 1
            """)
            print("✓ Added version_number column")
        
        if 'is_active' in missing_columns:
            cursor.execute("""
                ALTER TABLE quiz ADD COLUMN is_active BOOLEAN DEFAULT 1
            """)
            print("✓ Added is_active column")
        
        if 'is_locked' in missing_columns:
            cursor.execute("""
                ALTER TABLE quiz ADD COLUMN is_locked BOOLEAN DEFAULT 0
            """)
            print("✓ Added is_locked column")
        
        if 'locked_at' in missing_columns:
            cursor.execute("""
                ALTER TABLE quiz ADD COLUMN locked_at DATETIME
            """)
            print("✓ Added locked_at column")
        
        # Update existing quizzes to have default values
        cursor.execute("""
            UPDATE quiz 
            SET version_number = 1, is_active = 1, is_locked = 0 
            WHERE version_number IS NULL OR is_active IS NULL OR is_locked IS NULL
        """)
        
        conn.commit()
        print("✓ Database migration completed successfully!")
        print("\nQuiz versioning is now enabled:")
        print("- Existing quizzes are marked as version 1 and active")
        print("- When a quiz with attempts is edited, a new version will be created")
        print("- Original quizzes with attempts will be locked to preserve results")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        conn.rollback()
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    
    db_path = 'instance/quiz.db'
    
    if not os.path.exists(db_path):
        print("Database file not found.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = [
            'original_quiz_id',
            'version_number', 
            'is_active',
            'is_locked',
            'locked_at'
        ]
        
        missing = [col for col in required_columns if col not in columns]
        
        if missing:
            print(f"❌ Migration verification failed. Missing columns: {missing}")
            return False
        
        # Check data integrity
        cursor.execute("""
            SELECT COUNT(*) FROM quiz 
            WHERE version_number IS NULL OR is_active IS NULL OR is_locked IS NULL
        """)
        
        null_count = cursor.fetchone()[0]
        
        if null_count > 0:
            print(f"❌ Migration verification failed. {null_count} quizzes have NULL values in versioning fields.")
            return False
        
        print("✓ Migration verification successful!")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error during verification: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during verification: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Quiz Management System - Database Migration")
    print("=" * 50)
    
    migrate_database()
    print("\n" + "=" * 50)
    verify_migration()
    
    print("\n" + "=" * 50)
    print("Migration complete! You can now run your application.")
    print("The quiz versioning system is ready to use.")
