#!/usr/bin/env python3
"""
Demo script for quiz export functionality.
This script demonstrates the quiz export features by creating sample data and showing export output.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, Question, User, generate_quiz_csv_data, format_question_for_export
from werkzeug.security import generate_password_hash

def demo_quiz_export():
    """Demonstrate the quiz export functionality"""
    with app.app_context():
        print("🎯 Quiz Export Functionality Demo")
        print("=" * 60)
        
        # Create demo teacher
        demo_teacher = User.query.filter_by(email='<EMAIL>').first()
        if not demo_teacher:
            demo_teacher = User(
                name='Demo Teacher',
                email='<EMAIL>',
                password=generate_password_hash('demo123'),
                unhashed_password='demo123',
                role='teacher',
                is_verified=True
            )
            db.session.add(demo_teacher)
            db.session.commit()
        
        # Create demo quiz
        demo_quiz = Quiz(
            title='Demo Math Quiz: Fractions & Decimals',
            description='A comprehensive quiz covering fractions, decimals, and percentage conversions for middle school students.',
            teacher_id=demo_teacher.id,
            time_limit=45,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False,
            randomize_questions=True,
            allow_calculator=False
        )
        db.session.add(demo_quiz)
        db.session.flush()
        
        # Create demo questions
        questions = []
        
        # Question 1: MCQ
        q1 = Question(
            quiz_id=demo_quiz.id,
            question_text='What is 3/4 expressed as a decimal?',
            question_type='mcq',
            option1='0.34',
            option2='0.75',
            option3='0.43',
            option4='0.57',
            correct_answer='2',
            marks=20
        )
        questions.append(q1)
        db.session.add(q1)
        
        # Question 2: MCQ
        q2 = Question(
            quiz_id=demo_quiz.id,
            question_text='Which fraction is equivalent to 50%?',
            question_type='mcq',
            option1='1/4',
            option2='1/2',
            option3='3/4',
            option4='2/3',
            correct_answer='2',
            marks=20
        )
        questions.append(q2)
        db.session.add(q2)
        
        # Question 3: Text
        q3 = Question(
            quiz_id=demo_quiz.id,
            question_text='Convert 0.125 to a fraction in its simplest form.',
            question_type='text',
            correct_answer='1/8',
            marks=25
        )
        questions.append(q3)
        db.session.add(q3)
        
        # Question 4: MCQ
        q4 = Question(
            quiz_id=demo_quiz.id,
            question_text='What percentage is 7/10?',
            question_type='mcq',
            option1='7%',
            option2='10%',
            option3='70%',
            option4='0.7%',
            correct_answer='3',
            marks=20
        )
        questions.append(q4)
        db.session.add(q4)
        
        # Question 5: Text
        q5 = Question(
            quiz_id=demo_quiz.id,
            question_text='Round 2.7834 to 2 decimal places.',
            question_type='text',
            correct_answer='2.78',
            marks=15
        )
        questions.append(q5)
        db.session.add(q5)
        
        db.session.commit()
        
        print(f"📝 Created demo quiz: '{demo_quiz.title}'")
        print(f"👨‍🏫 Teacher: {demo_teacher.name}")
        print(f"📊 Questions: {len(questions)} total")
        print(f"⏱️  Time Limit: {demo_quiz.time_limit} minutes")
        print(f"🎯 Total Marks: {demo_quiz.total_marks}")
        print(f"📈 Difficulty: {demo_quiz.difficulty}")
        print()
        
        # Demonstrate question formatting
        print("🔍 Question Formatting Demo")
        print("-" * 40)
        
        for i, question in enumerate(questions, 1):
            formatted = format_question_for_export(question)
            print(f"\nQuestion {i}: {formatted['question_type'].upper()}")
            print(f"Text: {formatted['question_text']}")
            
            if formatted['question_type'] == 'mcq':
                print("Options:")
                for j in range(1, 5):
                    option = formatted.get(f'option{j}')
                    if option:
                        print(f"  {chr(64+j)}. {option}")
                print(f"Correct: {formatted['correct_answer_display']}")
            else:
                print(f"Answer: {formatted['correct_answer_display']}")
            
            print(f"Marks: {formatted['marks']}")
        
        print("\n" + "=" * 60)
        print("📄 CSV Export Preview")
        print("-" * 40)
        
        # Generate CSV data
        csv_data = generate_quiz_csv_data(demo_quiz, questions)
        
        # Show first 20 lines of CSV
        csv_lines = csv_data.split('\n')
        preview_lines = csv_lines[:25]
        
        for line in preview_lines:
            print(line)
        
        if len(csv_lines) > 25:
            print(f"... ({len(csv_lines) - 25} more lines)")
        
        print("\n" + "=" * 60)
        print("📊 Export Statistics")
        print("-" * 40)
        
        csv_size = len(csv_data.encode('utf-8'))
        print(f"CSV Size: {csv_size:,} bytes ({csv_size/1024:.1f} KB)")
        print(f"Total Lines: {len(csv_lines)}")
        print(f"Questions Exported: {len(questions)}")
        print(f"MCQ Questions: {sum(1 for q in questions if q.question_type == 'mcq')}")
        print(f"Text Questions: {sum(1 for q in questions if q.question_type == 'text')}")
        
        # Show export URLs
        print("\n" + "=" * 60)
        print("🔗 Export URLs")
        print("-" * 40)
        print(f"CSV Export: /export/quiz/{demo_quiz.id}/csv")
        print(f"PDF Export: /export/quiz/{demo_quiz.id}/pdf")
        print(f"Quiz View: /teacher/view-quiz/{demo_quiz.id}")
        print(f"My Quizzes: /teacher/my-quizzes")
        
        # Show file naming
        import re
        safe_title = re.sub(r'[^\w\s-]', '', demo_quiz.title).strip()
        safe_title = re.sub(r'[-\s]+', '-', safe_title)
        
        print("\n" + "=" * 60)
        print("📁 Generated Filenames")
        print("-" * 40)
        print(f"CSV: quiz-{safe_title}-{demo_quiz.id}.csv")
        print(f"PDF: quiz-{safe_title}-{demo_quiz.id}.pdf")
        
        # Cleanup
        print("\n" + "=" * 60)
        print("🧹 Cleanup")
        print("-" * 40)
        
        try:
            # Delete questions first
            for question in questions:
                db.session.delete(question)
            
            # Delete quiz
            db.session.delete(demo_quiz)
            
            # Delete demo teacher
            db.session.delete(demo_teacher)
            
            db.session.commit()
            print("✅ Demo data cleaned up successfully")
        except Exception as e:
            print(f"⚠️  Warning: Could not clean up all demo data: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 Quiz Export Demo Complete!")
        print("-" * 40)
        print("Features Demonstrated:")
        print("✅ Quiz creation with mixed question types")
        print("✅ Question formatting for export")
        print("✅ CSV data generation")
        print("✅ File naming and safety")
        print("✅ Export URL structure")
        print("✅ Data cleanup")
        print("\nTo test the export functionality:")
        print("1. Start the Flask application: python app.py")
        print("2. Login as a teacher")
        print("3. Go to 'My Quizzes'")
        print("4. Click the 'Export' dropdown on any quiz")
        print("5. Choose CSV or PDF format")
        print("6. File will download automatically")
        
        return True

if __name__ == "__main__":
    try:
        success = demo_quiz_export()
        if success:
            print("\n✅ Demo completed successfully!")
        else:
            print("\n❌ Demo failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
