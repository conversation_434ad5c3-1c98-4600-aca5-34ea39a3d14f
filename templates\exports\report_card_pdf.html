<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Card - {{ student.name }}</title>
    <style>
        @page {
            size: A4;
            margin: 0.75in;
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10px;
                color: #666;
            }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        /* Header Section */
        .report-header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 8px;
        }
        
        .school-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px auto;
            background: #2c3e50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .report-header h1 {
            color: #2c3e50;
            font-size: 28px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        
        .report-header .subtitle {
            color: #666;
            font-size: 16px;
            margin: 5px 0;
        }
        
        /* Student Information */
        .student-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .student-info h2 {
            color: #2c3e50;
            font-size: 20px;
            margin: 0 0 15px 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
        }
        
        /* Summary Statistics */
        .summary-stats {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            page-break-inside: avoid;
        }
        
        .summary-stats h3 {
            color: #0066cc;
            margin: 0 0 15px 0;
            font-size: 18px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #cce7ff;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #0066cc;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        
        /* Teacher Comment */
        .teacher-comment {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            page-break-inside: avoid;
        }
        
        .teacher-comment h3 {
            color: #856404;
            margin: 0 0 15px 0;
            font-size: 18px;
        }
        
        .comment-text {
            color: #856404;
            font-style: italic;
            line-height: 1.8;
        }
        
        .comment-meta {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ffeaa7;
            font-size: 12px;
            color: #856404;
        }
        
        /* Quiz Attempts Section */
        .quiz-attempts {
            margin-top: 30px;
        }
        
        .section-header {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }
        
        .section-header h3 {
            margin: 0;
            font-size: 18px;
        }
        
        .attempts-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
        }
        
        .attempts-table th,
        .attempts-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .attempts-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .attempts-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .quiz-title {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .score-cell {
            text-align: center;
        }
        
        .score-value {
            font-weight: bold;
            font-size: 16px;
        }
        
        .score-percentage {
            font-size: 12px;
            color: #666;
        }
        
        .grade-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
        }
        
        .grade-a { background: #d4edda; color: #155724; }
        .grade-b { background: #cce7ff; color: #004085; }
        .grade-c { background: #fff3cd; color: #856404; }
        .grade-d { background: #f8d7da; color: #721c24; }
        .grade-f { background: #f5c6cb; color: #721c24; }
        
        .date-cell {
            font-size: 12px;
            color: #666;
        }
        
        /* Quiz Feedback */
        .quiz-feedback {
            margin-top: 30px;
        }
        
        .feedback-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            page-break-inside: avoid;
        }
        
        .feedback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .feedback-quiz {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .feedback-date {
            font-size: 12px;
            color: #666;
        }
        
        .feedback-text {
            color: #495057;
            line-height: 1.6;
        }
        
        .feedback-teacher {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            font-style: italic;
        }
        
        /* Chart Placeholder */
        .chart-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            page-break-inside: avoid;
        }
        
        .chart-section h3 {
            color: #2c3e50;
            margin: 0 0 15px 0;
        }
        
        .chart-placeholder {
            height: 200px;
            background: white;
            border: 2px dashed #dee2e6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
        }
        
        /* Footer */
        .report-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        
        .export-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        /* Print-specific styles */
        @media print {
            .student-info,
            .summary-stats,
            .teacher-comment,
            .feedback-item,
            .chart-section {
                page-break-inside: avoid;
            }
            
            .quiz-attempts {
                page-break-before: auto;
            }
        }
        
        /* No data states */
        .no-data {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
        
        .no-data i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="report-header">
        <div class="school-logo">QMS</div>
        <h1>Student Report Card</h1>
        <div class="subtitle">Academic Performance Report</div>
        <div class="subtitle">Generated on {{ export_date }}</div>
    </div>
    
    <!-- Student Information -->
    <div class="student-info">
        <h2>Student Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Student Name:</span>
                <span class="info-value">{{ student.name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Student ID:</span>
                <span class="info-value">{{ student.id }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ student.email }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Class:</span>
                <span class="info-value">{{ student.role|title }}</span>
            </div>
            {% if student.parent_email %}
            <div class="info-item">
                <span class="info-label">Parent Email:</span>
                <span class="info-value">{{ student.parent_email }}</span>
            </div>
            {% endif %}
            <div class="info-item">
                <span class="info-label">Report Period:</span>
                <span class="info-value">{{ report_period or 'All Time' }}</span>
            </div>
        </div>
    </div>
    
    <!-- Summary Statistics -->
    <div class="summary-stats">
        <h3>Performance Summary</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">{{ attempts|length }}</div>
                <div class="stat-label">Total Quizzes</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ "%.1f"|format(average_score) }}%</div>
                <div class="stat-label">Average Score</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ "%.1f"|format(highest_score) }}%</div>
                <div class="stat-label">Highest Score</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ "%.1f"|format(lowest_score) }}%</div>
                <div class="stat-label">Lowest Score</div>
            </div>
        </div>
    </div>
    
    <!-- Teacher Comment -->
    {% if teacher_comment %}
    <div class="teacher-comment">
        <h3>Teacher's Overall Comment</h3>
        <div class="comment-text">{{ teacher_comment.comment_text }}</div>
        <div class="comment-meta">
            <strong>Teacher:</strong> {{ teacher_comment.teacher.name }} | 
            <strong>Date:</strong> {{ teacher_comment.timestamp.strftime('%B %d, %Y') }}
        </div>
    </div>
    {% endif %}
    
    <!-- Performance Chart Placeholder -->
    <div class="chart-section">
        <h3>Score Trends</h3>
        <div class="chart-placeholder">
            Performance chart would be displayed here<br>
            (Showing score progression over time)
        </div>
    </div>
    
    <!-- Quiz Attempts -->
    <div class="quiz-attempts">
        <div class="section-header">
            <h3>Quiz Attempts ({{ attempts|length }} total)</h3>
        </div>
        
        {% if attempts %}
        <table class="attempts-table">
            <thead>
                <tr>
                    <th>Quiz Title</th>
                    <th>Date Taken</th>
                    <th>Score</th>
                    <th>Total Marks</th>
                    <th>Percentage</th>
                    <th>Grade</th>
                </tr>
            </thead>
            <tbody>
                {% for attempt in attempts %}
                <tr>
                    <td class="quiz-title">{{ attempt.quiz.title }}</td>
                    <td class="date-cell">{{ attempt.submitted_at.strftime('%b %d, %Y') }}</td>
                    <td class="score-cell">
                        <span class="score-value">{{ "%.1f"|format(attempt.score) }}%</span>
                    </td>
                    <td class="score-cell">{{ attempt.quiz.total_marks }}</td>
                    <td class="score-cell">
                        <span class="score-percentage">{{ "%.1f"|format(attempt.score) }}%</span>
                    </td>
                    <td class="score-cell">
                        {% set grade = get_grade(attempt.score, attempt.quiz) %}
                        <span class="grade-badge grade-{{ grade|lower }}">{{ grade }}</span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="no-data">
            <div>📊</div>
            <p>No quiz attempts found for this student.</p>
        </div>
        {% endif %}
    </div>
    
    <!-- Quiz Feedback -->
    {% if quiz_feedback %}
    <div class="quiz-feedback">
        <div class="section-header">
            <h3>Teacher Feedback on Quiz Attempts</h3>
        </div>
        
        {% for feedback in quiz_feedback %}
        <div class="feedback-item">
            <div class="feedback-header">
                <span class="feedback-quiz">{{ feedback.attempt.quiz.title }}</span>
                <span class="feedback-date">{{ feedback.timestamp.strftime('%B %d, %Y') }}</span>
            </div>
            <div class="feedback-text">{{ feedback.comment_text }}</div>
            <div class="feedback-teacher">— {{ feedback.teacher.name }}</div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Footer -->
    <div class="report-footer">
        <div>Quiz Management System - Student Report Card</div>
        <div class="export-info">
            <strong>Export Details:</strong> Generated on {{ export_date }} | 
            Student ID: {{ student.id }} | 
            Total Attempts: {{ attempts|length }} | 
            Average Score: {{ "%.1f"|format(average_score) }}%
        </div>
    </div>
</body>
</html>
