{% extends "base.html" %}

{% block title %}Teacher Dashboard{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* Dashboard Common Styles (Copied from admin - consider moving to shared CSS) */
.page-content.dashboard {
    background-color: #f8f9fa;
    padding: 2rem 0;
    flex-grow: 1;
}
.dashboard-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 1rem;
}
.welcome-message {
    margin-bottom: 1.5rem;
    font-size: 1.2em;
    color: #555;
}
.dashboard-card {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}
.dashboard-card h2, .dashboard-card h3 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #343a40;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.8rem;
}
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Adjust minmax */
    gap: 1.5rem;
    margin-top: 1rem; /* Reduced top margin */
}

/* Teacher Dashboard Specific Styles */
.action-card p {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 0.95em;
    min-height: 40px; /* Give some space for description */
}

.action-card .btn {
    display: inline-block;
    padding: 0.7rem 1.5rem;
    background-color: dodgerblue;
    color: white;
    text-decoration: none;
    border-radius: 8px; /* Match card radius */
    transition: background-color 0.3s, box-shadow 0.3s;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.action-card .btn:hover {
    background-color: #0056b3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

</style>
{% endblock %}

{% block content %}
<main class="page-content dashboard"> {# Use generic dashboard class #}
    <div class="dashboard-container">
        <h1>Teacher Dashboard</h1>
        <p class="welcome-message">Welcome, {{ session.user_name }}!</p>

        <div class="dashboard-grid"> {# Use a grid container #}
            <div class="dashboard-card action-card"> {# Use dashboard-card #}
                <h3>Create Quiz</h3>
                <p>Design and publish a new quiz for your students.</p>
                <a href="{{ url_for('create_quiz') }}" class="fancy">
                    <span class="top-key"></span>
                    <span class="text">Create New Quiz</span>
                    <span class="bottom-key-1"></span>
                    <span class="bottom-key-2"></span>
                </a>
            </div>

            <div class="dashboard-card action-card">
                <h3>My Quizzes</h3>
                <p>View, edit, or delete your previously created quizzes.</p>
                <a href="{{ url_for('my_quizzes') }}" class="fancy">
                    <span class="top-key"></span>
                    <span class="text">Manage My Quizzes</span>
                    <span class="bottom-key-1"></span>
                    <span class="bottom-key-2"></span>
                </a>
            </div>

            <div class="dashboard-card action-card">
                <h3>View Results</h3>
                <p>Review student performance and quiz submissions.</p>
                {# Link to results view - Needs Implementation #}
                <a href="#" class="fancy">
                    <span class="top-key"></span>
                    <span class="text">View Student Results</span> 
                    <span class="bottom-key-1"></span>
                    <span class="bottom-key-2"></span>
                </a>
            </div>
            
            <div class="dashboard-card action-card">
                <h3>Messages</h3>
                <p>Communicate with students and parents via the inbox.</p>
                <a href="{{ url_for('inbox') }}" class="fancy">
                    <span class="top-key"></span>
                    <span class="text">Go to Inbox</span>
                    <span class="bottom-key-1"></span>
                    <span class="bottom-key-2"></span>
                </a>
            </div>

            <div class="dashboard-card action-card">
                <h3>Student Reports</h3>
                <p>Add comments and notes to student report cards.</p>
                <a href="{{ url_for('manage_student_reports') }}" class="fancy">
                    <span class="top-key"></span>
                    <span class="text">Manage Reports</span>
                    <span class="bottom-key-1"></span>
                    <span class="bottom-key-2"></span>
                </a>
            </div>

            <div class="dashboard-card action-card">
                <h3>Parent Comments</h3>
                <p>View and respond to parent feedback on quiz attempts.</p>
                <a href="{{ url_for('teacher_view_comments') }}" class="fancy">
                    <span class="top-key"></span>
                    <span class="text">View Comments</span>
                    <span class="bottom-key-1"></span>
                    <span class="bottom-key-2"></span>
                </a>
            </div>
        </div>
    </div>
</main>
{% endblock %}