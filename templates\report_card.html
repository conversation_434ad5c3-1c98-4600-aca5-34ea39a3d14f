{% extends "base.html" %}

{% block title %}Report Card - {{ child.name }}{% endblock %}

{% block content %}
<div class="container report-card-container">
    <div class="report-header">
        <h1>Report Card</h1>
        <h2>{{ child.name }}</h2>
        <div class="header-actions">
            <a href="{{ url_for('parent_dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
            <!-- <a href="{{ url_for('view_report_card', child_id=child.id, format='pdf') }}" class="btn btn-sm btn-danger">Export PDF</a> -->
        </div>
    </div>

    <div class="report-section summary-section">
        <h3>Overall Summary</h3>
        <p><strong>Cumulative Average Score:</strong> {{ "%.2f"|format(average_score) }}%</p>
        <p><strong>Total Quizzes Attempted:</strong> {{ attempts|length }}</p>
    </div>

    <div class="report-section attempts-section">
        <h3>Quiz Attempts & Teacher Feedback</h3>
        {% if attempts_with_feedback %}
            {% for item in attempts_with_feedback %}
            <div class="attempt-card">
                <div class="attempt-header">
                    <div class="attempt-info">
                        <h4>{{ item.attempt.quiz.title }}</h4>
                        <div class="attempt-meta">
                            <span class="date">{{ item.attempt.submitted_at.strftime('%B %d, %Y') }}</span>
                            <span class="score">Score: {{ "%.1f"|format(item.attempt.score) }}%</span>
                            <span class="grade grade-{{ item.attempt.grade|lower }}">{{ item.attempt.grade }}</span>
                        </div>
                    </div>
                    <div class="attempt-actions">
                        <a href="{{ url_for('view_child_result', attempt_id=item.attempt.id) }}" class="btn btn-sm btn-info">View Details</a>
                    </div>
                </div>

                {% if item.quiz_feedback %}
                <div class="teacher-feedback">
                    <h5>Teacher Feedback:</h5>
                    <div class="feedback-content">
                        <div class="feedback-text">{{ item.quiz_feedback.comment_text }}</div>
                        <div class="feedback-meta">
                            <span class="feedback-teacher">{{ item.quiz_feedback.teacher.name }}</span>
                            <span class="feedback-date">{{ item.quiz_feedback.timestamp.strftime('%B %d, %Y') }}</span>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <p>No quiz attempts recorded yet.</p>
        {% endif %}
    </div>

    <!-- Teacher Comments Section -->
    <div class="report-section comments-section">
        <h3>Teacher Comments</h3>

        {% if report_comments %}
            {% for comment in report_comments %}
            <div class="teacher-comment">
                <div class="comment-header">
                    <span class="teacher-name">{{ comment.teacher.name }}</span>
                    <span class="comment-date">{{ comment.timestamp.strftime('%B %d, %Y') }}</span>
                </div>
                <div class="comment-text">{{ comment.comment_text }}</div>
            </div>
            {% endfor %}
        {% elif child.report_comment %}
            <!-- Legacy comment display -->
            <div class="teacher-comment legacy-comment">
                <div class="comment-header">
                    <span class="teacher-name">Teacher Comment (Legacy)</span>
                </div>
                <div class="comment-text">{{ child.report_comment | e | replace('\n', '<br>') | safe }}</div>
            </div>
        {% else %}
            <p class="no-comments">No teacher comments available yet.</p>
        {% endif %}
    </div>

</div>

<style>
/* Basic Report Card Styling */
.report-card-container {
    max-width: 850px;
    margin: 2rem auto;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.report-header {
    background-color: #f8f9fa;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    text-align: center;
    position: relative;
}

.report-header h1 {
    color: #007bff;
    margin-bottom: 0.2rem;
}
.report-header h2 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: normal;
}

.header-actions {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    display: flex;
    gap: 0.5rem;
}

.report-section {
    padding: 1.5rem 2rem;
    margin-bottom: 1rem;
}

.report-section h3 {
    color: #17a2b8;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.6rem;
    margin-bottom: 1.2rem;
    font-size: 1.3rem;
}

.summary-section p {
    font-size: 1.05rem;
    margin-bottom: 0.5rem;
}
.summary-section p strong {
    color: #343a40;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}
.report-table th, .report-table td {
    border: 1px solid #dee2e6;
    padding: 0.7rem 0.9rem;
    text-align: left;
    vertical-align: middle;
}
.report-table th {
    background-color: #e9ecef;
    font-weight: 600;
}
.report-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.comment-box {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 1rem 1.5rem;
    min-height: 80px;
    line-height: 1.6;
    white-space: pre-wrap;
}

/* Button Styles */
.btn {
    display: inline-block; font-weight: 600; text-align: center; vertical-align: middle;
    cursor: pointer; user-select: none; border: 1px solid transparent; padding: 0.5rem 1rem;
    font-size: 0.9rem; border-radius: 0.25rem; transition: all .15s ease-in-out;
}
.btn-sm {
     padding: 0.375rem 0.75rem; font-size: 0.875rem;
}
.btn-xs {
     padding: 0.25rem 0.5rem; font-size: 0.75rem; line-height: 1.5; border-radius: 0.2rem;
}

.btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
.btn-secondary:hover { background-color: #5a6268; border-color: #545b62; }
.btn-danger { color: #fff; background-color: #dc3545; border-color: #dc3545; }
.btn-danger:hover { background-color: #c82333; border-color: #bd2130; }
.btn-info { color: #fff; background-color: #17a2b8; border-color: #17a2b8; }
.btn-info:hover { background-color: #138496; border-color: #117a8b; }

/* Attempt Cards */
.attempt-card {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background: #f8f9fa;
}

.attempt-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.attempt-info h4 {
    margin: 0 0 0.5rem 0;
    color: #007bff;
}

.attempt-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    flex-wrap: wrap;
}

.attempt-meta .score {
    font-weight: 600;
    color: #495057;
}

.attempt-meta .grade {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: bold;
    font-size: 0.8rem;
}

.grade.grade-a { background: #d4edda; color: #155724; }
.grade.grade-b { background: #cce7ff; color: #004085; }
.grade.grade-c { background: #fff3cd; color: #856404; }
.grade.grade-d { background: #f8d7da; color: #721c24; }
.grade.grade-f { background: #f8d7da; color: #721c24; }

.attempt-meta .date {
    color: #6c757d;
}

.teacher-feedback {
    background: white;
    border-radius: 4px;
    padding: 1rem;
    margin-top: 1rem;
    border-left: 4px solid #28a745;
}

.teacher-feedback h5 {
    margin: 0 0 0.75rem 0;
    color: #28a745;
    font-size: 1rem;
}

.feedback-content {
    display: grid;
    gap: 0.5rem;
}

.feedback-text {
    color: #495057;
    line-height: 1.5;
}

.feedback-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #6c757d;
}

.feedback-teacher {
    font-weight: 600;
}

/* Teacher Comments */
.teacher-comment {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #007bff;
}

.teacher-comment.legacy-comment {
    border-left-color: #6c757d;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.teacher-name {
    font-weight: 600;
    color: #495057;
}

.comment-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.comment-text {
    color: #495057;
    line-height: 1.5;
}

.no-comments {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .attempt-header {
        flex-direction: column;
        gap: 1rem;
    }

    .attempt-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .feedback-meta {
        flex-direction: column;
        gap: 0.25rem;
    }
}

</style>
{% endblock %}