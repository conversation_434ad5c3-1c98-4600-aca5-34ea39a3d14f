<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Export - {{ quiz.title }}</title>
    <style>
        @page {
            size: A4;
            margin: 1in;
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10px;
                color: #666;
            }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #007bff;
            font-size: 28px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        
        .header .subtitle {
            color: #666;
            font-size: 14px;
            margin: 5px 0;
        }
        
        .quiz-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .quiz-info h2 {
            color: #495057;
            font-size: 18px;
            margin: 0 0 15px 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
        }
        
        .grading-section {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            page-break-inside: avoid;
        }
        
        .grading-section h3 {
            color: #0066cc;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        
        .grade-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }
        
        .grade-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #cce7ff;
        }
        
        .grade-letter {
            font-weight: bold;
            font-size: 18px;
            color: #0066cc;
        }
        
        .grade-threshold {
            font-size: 12px;
            color: #666;
        }
        
        .questions-section {
            margin-top: 30px;
        }
        
        .questions-header {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }
        
        .questions-header h2 {
            margin: 0;
            font-size: 20px;
        }
        
        .question {
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 20px;
            margin-bottom: 0;
            page-break-inside: avoid;
        }
        
        .question:last-child {
            border-radius: 0 0 8px 8px;
        }
        
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .question-number {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 14px;
        }
        
        .question-meta {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .question-type {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .question-marks {
            background: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .question-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .options {
            margin: 15px 0;
        }
        
        .option {
            display: flex;
            align-items: flex-start;
            margin: 8px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #dee2e6;
        }
        
        .option-letter {
            font-weight: bold;
            color: #495057;
            margin-right: 10px;
            min-width: 20px;
        }
        
        .option-text {
            flex: 1;
            color: #495057;
        }
        
        .correct-answer {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 12px;
            margin-top: 15px;
        }
        
        .correct-answer-label {
            font-weight: bold;
            color: #155724;
            margin-bottom: 5px;
        }
        
        .correct-answer-text {
            color: #155724;
            font-weight: 500;
        }
        
        .text-answer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
            font-style: italic;
            color: #495057;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        
        .export-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }
        
        /* Print-specific styles */
        @media print {
            .question {
                page-break-inside: avoid;
            }
            
            .quiz-info {
                page-break-inside: avoid;
            }
            
            .grading-section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>{{ quiz.title }}</h1>
        <div class="subtitle">Quiz Export</div>
        <div class="subtitle">Generated on {{ export_date }}</div>
    </div>
    
    <!-- Quiz Information -->
    <div class="quiz-info">
        <h2>Quiz Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Teacher:</span>
                <span class="info-value">{{ quiz.teacher.name }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Created:</span>
                <span class="info-value">{{ quiz.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Time Limit:</span>
                <span class="info-value">{{ quiz.time_limit }} minutes</span>
            </div>
            <div class="info-item">
                <span class="info-label">Total Marks:</span>
                <span class="info-value">{{ quiz.total_marks }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Difficulty:</span>
                <span class="info-value">{{ quiz.difficulty.title() }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Version:</span>
                <span class="info-value">v{{ quiz.version_number }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Status:</span>
                <span class="info-value">
                    {% if quiz.is_locked %}Locked{% elif quiz.is_active %}Active{% else %}Inactive{% endif %}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Question Randomization:</span>
                <span class="info-value">{{ 'Enabled' if quiz.randomize_questions else 'Disabled' }}</span>
            </div>
        </div>
        
        {% if quiz.description %}
        <div style="margin-top: 15px;">
            <div class="info-label">Description:</div>
            <div style="margin-top: 5px; color: #495057;">{{ quiz.description }}</div>
        </div>
        {% endif %}
        
        <div style="margin-top: 15px;">
            <div class="info-item">
                <span class="info-label">Calculator Access:</span>
                <span class="info-value">{{ 'Allowed' if quiz.allow_calculator else 'Not Allowed' }}</span>
            </div>
        </div>
    </div>
    
    <!-- Grading Thresholds -->
    <div class="grading-section">
        <h3>Grading Thresholds</h3>
        <div class="grade-grid">
            <div class="grade-item">
                <div class="grade-letter">A</div>
                <div class="grade-threshold">{{ quiz.grade_a_threshold }}%+</div>
            </div>
            <div class="grade-item">
                <div class="grade-letter">B</div>
                <div class="grade-threshold">{{ quiz.grade_b_threshold }}%+</div>
            </div>
            <div class="grade-item">
                <div class="grade-letter">C</div>
                <div class="grade-threshold">{{ quiz.grade_c_threshold }}%+</div>
            </div>
            <div class="grade-item">
                <div class="grade-letter">D</div>
                <div class="grade-threshold">{{ quiz.grade_d_threshold }}%+</div>
            </div>
        </div>
    </div>
    
    <!-- Questions -->
    <div class="questions-section">
        <div class="questions-header">
            <h2>Questions ({{ questions|length }} total)</h2>
        </div>
        
        {% for question in questions %}
        <div class="question">
            <div class="question-header">
                <div class="question-number">{{ loop.index }}</div>
                <div class="question-meta">
                    <span class="question-type">{{ question.question_type }}</span>
                    <span class="question-marks">{{ question.marks }} marks</span>
                </div>
            </div>
            
            <div class="question-text">{{ question.question_text }}</div>
            
            {% if question.question_type == 'mcq' %}
            <div class="options">
                {% if question.option1 %}
                <div class="option">
                    <span class="option-letter">A.</span>
                    <span class="option-text">{{ question.option1 }}</span>
                </div>
                {% endif %}
                {% if question.option2 %}
                <div class="option">
                    <span class="option-letter">B.</span>
                    <span class="option-text">{{ question.option2 }}</span>
                </div>
                {% endif %}
                {% if question.option3 %}
                <div class="option">
                    <span class="option-letter">C.</span>
                    <span class="option-text">{{ question.option3 }}</span>
                </div>
                {% endif %}
                {% if question.option4 %}
                <div class="option">
                    <span class="option-letter">D.</span>
                    <span class="option-text">{{ question.option4 }}</span>
                </div>
                {% endif %}
            </div>
            {% endif %}
            
            <div class="correct-answer">
                <div class="correct-answer-label">Correct Answer:</div>
                <div class="correct-answer-text">{{ question.correct_answer_display }}</div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Footer -->
    <div class="footer">
        <div>Quiz Management System - Quiz Export</div>
        <div class="export-info">
            <strong>Export Details:</strong> Generated on {{ export_date }} | 
            Quiz ID: {{ quiz.id }} | 
            Total Questions: {{ questions|length }} | 
            Total Marks: {{ quiz.total_marks }}
        </div>
    </div>
</body>
</html>
