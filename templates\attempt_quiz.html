{% extends "base.html" %}

{% block title %}{{ quiz.title }} - Attempt Quiz{% endblock %}

{% block content %}
<div class="container quiz-attempt-container">
    <div class="quiz-header">
        <h1>{{ quiz.title }}</h1>
        <div class="header-controls">
            <div id="timer">Time Left: <span>{{ quiz.time_limit }}:00</span></div>
            {% if quiz.allow_calculator %}
            <button type="button" id="calculator-toggle" class="btn btn-calculator">
                <span class="calculator-icon">🔢</span>
                <span class="calculator-text">Calculator</span>
            </button>
            {% endif %}
        </div>
    </div>
    {% if quiz.description %}
        <p class="quiz-description">{{ quiz.description }}</p>
    {% endif %}

    <form id="quiz-form" method="post" action="{{ url_for('submit_quiz', quiz_id=quiz.id) }}">
        {% for question in questions %}
        <div class="question-card" id="question-{{ question.id }}">
            <div class="question-header">
                 <span class="question-number">Question {{ loop.index }}</span>
                 <span class="question-marks">({{ question.marks }} Marks)</span>
            </div>
           
            <p class="question-text">{{ question.question_text }}</p>

            <div class="options">
                {% if question.question_type == 'mcq' %}
                    {% set options = [question.option1, question.option2, question.option3, question.option4] %}
                    {% for i in range(options|length) %}
                        {% if options[i] %}
                            <div class="option-item">
                                <input type="radio" id="q{{ question.id }}_opt{{ i+1 }}" name="question_{{ question.id }}" value="{{ i+1 }}">
                                <label for="q{{ question.id }}_opt{{ i+1 }}" class="option-label">
                                    <span class="option-text">{{ options[i] }}</span>
                                    <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                                </label>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% elif question.question_type == 'true_false' %}
                     <div class="option-item">
                        <input type="radio" id="q{{ question.id }}_true" name="question_{{ question.id }}" value="True">
                         <label for="q{{ question.id }}_true" class="option-label">
                            <span class="option-text">True</span>
                             <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                        </label>
                    </div>
                     <div class="option-item">
                        <input type="radio" id="q{{ question.id }}_false" name="question_{{ question.id }}" value="False">
                         <label for="q{{ question.id }}_false" class="option-label">
                            <span class="option-text">False</span>
                             <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                        </label>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <button type="submit" class="btn submit-btn">Submit Quiz</button>
    </form>
</div>

<!-- Calculator Modal -->
{% if quiz.allow_calculator %}
<div id="calculator-modal" class="calculator-modal">
    <div class="calculator-container">
        <div class="calculator-header">
            <h3>Calculator</h3>
            <div class="calculator-controls">
                <button type="button" id="calculator-mode-toggle" class="btn btn-sm btn-mode">Scientific</button>
                <button type="button" id="calculator-close" class="btn btn-sm btn-close">×</button>
            </div>
        </div>

        <div class="calculator-body">
            <div class="calculator-display">
                <input type="text" id="calculator-screen" readonly value="0" />
            </div>

            <!-- Basic Calculator -->
            <div id="basic-calculator" class="calculator-grid">
                <button type="button" class="calc-btn calc-clear" data-action="clear">C</button>
                <button type="button" class="calc-btn calc-clear" data-action="clear-entry">CE</button>
                <button type="button" class="calc-btn calc-operator" data-action="backspace">⌫</button>
                <button type="button" class="calc-btn calc-operator" data-action="divide">÷</button>

                <button type="button" class="calc-btn calc-number" data-number="7">7</button>
                <button type="button" class="calc-btn calc-number" data-number="8">8</button>
                <button type="button" class="calc-btn calc-number" data-number="9">9</button>
                <button type="button" class="calc-btn calc-operator" data-action="multiply">×</button>

                <button type="button" class="calc-btn calc-number" data-number="4">4</button>
                <button type="button" class="calc-btn calc-number" data-number="5">5</button>
                <button type="button" class="calc-btn calc-number" data-number="6">6</button>
                <button type="button" class="calc-btn calc-operator" data-action="subtract">−</button>

                <button type="button" class="calc-btn calc-number" data-number="1">1</button>
                <button type="button" class="calc-btn calc-number" data-number="2">2</button>
                <button type="button" class="calc-btn calc-number" data-number="3">3</button>
                <button type="button" class="calc-btn calc-operator" data-action="add">+</button>

                <button type="button" class="calc-btn calc-number" data-number="0">0</button>
                <button type="button" class="calc-btn calc-operator" data-action="decimal">.</button>
                <button type="button" class="calc-btn calc-operator" data-action="percent">%</button>
                <button type="button" class="calc-btn calc-equals" data-action="equals">=</button>
            </div>

            <!-- Scientific Calculator (Hidden by default) -->
            <div id="scientific-calculator" class="calculator-grid scientific-grid" style="display: none;">
                <button type="button" class="calc-btn calc-function" data-action="sin">sin</button>
                <button type="button" class="calc-btn calc-function" data-action="cos">cos</button>
                <button type="button" class="calc-btn calc-function" data-action="tan">tan</button>
                <button type="button" class="calc-btn calc-function" data-action="log">log</button>

                <button type="button" class="calc-btn calc-function" data-action="sqrt">√</button>
                <button type="button" class="calc-btn calc-function" data-action="power">x²</button>
                <button type="button" class="calc-btn calc-function" data-action="pi">π</button>
                <button type="button" class="calc-btn calc-function" data-action="e">e</button>

                <button type="button" class="calc-btn calc-operator" data-action="open-paren">(</button>
                <button type="button" class="calc-btn calc-operator" data-action="close-paren">)</button>
                <button type="button" class="calc-btn calc-function" data-action="factorial">x!</button>
                <button type="button" class="calc-btn calc-function" data-action="inverse">1/x</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
// --- Timer Logic --- 
const timerDisplay = document.querySelector('#timer span');
const quizForm = document.getElementById('quiz-form');
// Ensure time_limit is treated as a number, default to 0 if invalid
const timeLimitMinutes = parseInt("{{ quiz.time_limit | default(0, true) }}", 10) || 0;
let timeLeft = timeLimitMinutes * 60; // Time in seconds

function updateTimer() {
    const minutes = Math.floor(timeLeft / 60);
    let seconds = timeLeft % 60;
    seconds = seconds < 10 ? '0' + seconds : seconds;
    timerDisplay.textContent = `${minutes}:${seconds}`;
    
    if (timeLeft <= 0) {
        clearInterval(timerInterval);
        alert('Time is up! Submitting your quiz.');
        quizForm.submit();
    } else {
        timeLeft--;
    }
}

const timerInterval = setInterval(updateTimer, 1000);

// --- Confirmation Before Submit --- 
quizForm.addEventListener('submit', function(event) {
    if (timeLeft > 0) { // Only ask for confirmation if time is not up
        const confirmation = confirm('Are you sure you want to submit your quiz?');
        if (!confirmation) {
            event.preventDefault(); // Stop submission if user cancels
        }
    }
    // If time is up, form submits automatically without confirmation
});

// --- Strikethrough Logic ---
function toggleStrike(button) {
    const label = button.closest('.option-label');
    const textSpan = label.querySelector('.option-text');
    textSpan.classList.toggle('strikethrough');
}

{% if quiz.allow_calculator %}
// Calculator functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeCalculator();
});

// Calculator Implementation
function initializeCalculator() {
    const calculatorToggle = document.getElementById('calculator-toggle');
    const calculatorModal = document.getElementById('calculator-modal');
    const calculatorClose = document.getElementById('calculator-close');
    const calculatorScreen = document.getElementById('calculator-screen');
    const calculatorModeToggle = document.getElementById('calculator-mode-toggle');
    const basicCalculator = document.getElementById('basic-calculator');
    const scientificCalculator = document.getElementById('scientific-calculator');

    let currentInput = '0';
    let previousInput = '';
    let operator = '';
    let waitingForOperand = false;
    let isScientificMode = false;
    let calculatorUsageLog = [];

    // Load saved state from sessionStorage
    loadCalculatorState();

    // Calculator toggle functionality
    calculatorToggle.addEventListener('click', function() {
        calculatorModal.style.display = 'flex';
        logCalculatorUsage('opened');
        calculatorScreen.focus();
    });

    calculatorClose.addEventListener('click', function() {
        calculatorModal.style.display = 'none';
        logCalculatorUsage('closed');
    });

    // Close calculator when clicking outside
    calculatorModal.addEventListener('click', function(e) {
        if (e.target === calculatorModal) {
            calculatorModal.style.display = 'none';
            logCalculatorUsage('closed');
        }
    });

    // Mode toggle functionality
    calculatorModeToggle.addEventListener('click', function() {
        isScientificMode = !isScientificMode;
        if (isScientificMode) {
            scientificCalculator.style.display = 'grid';
            calculatorModeToggle.textContent = 'Basic';
            logCalculatorUsage('switched_to_scientific');
        } else {
            scientificCalculator.style.display = 'none';
            calculatorModeToggle.textContent = 'Scientific';
            logCalculatorUsage('switched_to_basic');
        }
    });

    // Number button handlers
    document.querySelectorAll('.calc-number').forEach(button => {
        button.addEventListener('click', function() {
            const number = this.getAttribute('data-number');
            inputNumber(number);
            logCalculatorUsage('number_input', number);
        });
    });

    // Operator button handlers
    document.querySelectorAll('.calc-operator').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            handleOperator(action);
            logCalculatorUsage('operator', action);
        });
    });

    // Function button handlers (scientific)
    document.querySelectorAll('.calc-function').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            handleFunction(action);
            logCalculatorUsage('function', action);
        });
    });

    // Equals button handler
    document.querySelectorAll('.calc-equals').forEach(button => {
        button.addEventListener('click', function() {
            calculate();
            logCalculatorUsage('equals');
        });
    });

    // Clear button handlers
    document.querySelectorAll('.calc-clear').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            if (action === 'clear') {
                clear();
                logCalculatorUsage('clear');
            } else if (action === 'clear-entry') {
                clearEntry();
                logCalculatorUsage('clear_entry');
            }
        });
    });

    // Keyboard support
    document.addEventListener('keydown', function(e) {
        if (calculatorModal.style.display === 'flex') {
            handleKeyboard(e);
        }
    });

    // Prevent copy/paste in calculator
    calculatorScreen.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    calculatorScreen.addEventListener('paste', function(e) {
        e.preventDefault();
    });

    calculatorScreen.addEventListener('copy', function(e) {
        e.preventDefault();
    });

    // Calculator functions
    function inputNumber(number) {
        if (waitingForOperand) {
            currentInput = number;
            waitingForOperand = false;
        } else {
            currentInput = currentInput === '0' ? number : currentInput + number;
        }
        updateDisplay();
        saveCalculatorState();
    }

    function handleOperator(action) {
        switch (action) {
            case 'add':
            case 'subtract':
            case 'multiply':
            case 'divide':
                performOperation(action);
                break;
            case 'decimal':
                inputDecimal();
                break;
            case 'percent':
                percentage();
                break;
            case 'backspace':
                backspace();
                break;
            case 'open-paren':
                inputNumber('(');
                break;
            case 'close-paren':
                inputNumber(')');
                break;
        }
    }

    function handleFunction(action) {
        const num = parseFloat(currentInput);
        let result;

        switch (action) {
            case 'sin':
                result = Math.sin(num * Math.PI / 180);
                break;
            case 'cos':
                result = Math.cos(num * Math.PI / 180);
                break;
            case 'tan':
                result = Math.tan(num * Math.PI / 180);
                break;
            case 'log':
                result = Math.log10(num);
                break;
            case 'sqrt':
                result = Math.sqrt(num);
                break;
            case 'power':
                result = Math.pow(num, 2);
                break;
            case 'pi':
                currentInput = Math.PI.toString();
                updateDisplay();
                saveCalculatorState();
                return;
            case 'e':
                currentInput = Math.E.toString();
                updateDisplay();
                saveCalculatorState();
                return;
            case 'factorial':
                result = factorial(num);
                break;
            case 'inverse':
                result = 1 / num;
                break;
        }

        if (result !== undefined) {
            currentInput = result.toString();
            updateDisplay();
            saveCalculatorState();
        }
    }

    function performOperation(nextOperator) {
        const inputValue = parseFloat(currentInput);

        if (previousInput === '') {
            previousInput = inputValue;
        } else if (operator) {
            const currentValue = previousInput || 0;
            const newValue = calculate(currentValue, inputValue, operator);

            currentInput = String(newValue);
            previousInput = newValue;
        }

        waitingForOperand = true;
        operator = nextOperator;
        updateDisplay();
        saveCalculatorState();
    }

    function calculate(firstOperand, secondOperand, operator) {
        switch (operator) {
            case 'add':
                return firstOperand + secondOperand;
            case 'subtract':
                return firstOperand - secondOperand;
            case 'multiply':
                return firstOperand * secondOperand;
            case 'divide':
                return firstOperand / secondOperand;
            default:
                return secondOperand;
        }
    }

    function calculate() {
        const inputValue = parseFloat(currentInput);

        if (previousInput !== '' && operator && !waitingForOperand) {
            const newValue = calculate(previousInput, inputValue, operator);
            currentInput = String(newValue);
            previousInput = '';
            operator = '';
            waitingForOperand = true;
            updateDisplay();
            saveCalculatorState();
        }
    }

    function inputDecimal() {
        if (waitingForOperand) {
            currentInput = '0.';
            waitingForOperand = false;
        } else if (currentInput.indexOf('.') === -1) {
            currentInput += '.';
        }
        updateDisplay();
        saveCalculatorState();
    }

    function percentage() {
        const num = parseFloat(currentInput);
        currentInput = (num / 100).toString();
        updateDisplay();
        saveCalculatorState();
    }

    function backspace() {
        if (currentInput.length > 1) {
            currentInput = currentInput.slice(0, -1);
        } else {
            currentInput = '0';
        }
        updateDisplay();
        saveCalculatorState();
    }

    function clear() {
        currentInput = '0';
        previousInput = '';
        operator = '';
        waitingForOperand = false;
        updateDisplay();
        saveCalculatorState();
    }

    function clearEntry() {
        currentInput = '0';
        updateDisplay();
        saveCalculatorState();
    }

    function factorial(n) {
        if (n < 0) return NaN;
        if (n === 0 || n === 1) return 1;
        let result = 1;
        for (let i = 2; i <= n; i++) {
            result *= i;
        }
        return result;
    }

    function updateDisplay() {
        calculatorScreen.value = currentInput;
    }

    function handleKeyboard(e) {
        const key = e.key;

        if (key >= '0' && key <= '9') {
            inputNumber(key);
            logCalculatorUsage('keyboard_number', key);
        } else if (key === '.') {
            inputDecimal();
            logCalculatorUsage('keyboard_decimal');
        } else if (key === '+') {
            handleOperator('add');
            logCalculatorUsage('keyboard_operator', 'add');
        } else if (key === '-') {
            handleOperator('subtract');
            logCalculatorUsage('keyboard_operator', 'subtract');
        } else if (key === '*') {
            handleOperator('multiply');
            logCalculatorUsage('keyboard_operator', 'multiply');
        } else if (key === '/') {
            e.preventDefault();
            handleOperator('divide');
            logCalculatorUsage('keyboard_operator', 'divide');
        } else if (key === 'Enter' || key === '=') {
            calculate();
            logCalculatorUsage('keyboard_equals');
        } else if (key === 'Escape') {
            calculatorModal.style.display = 'none';
            logCalculatorUsage('keyboard_close');
        } else if (key === 'Backspace') {
            backspace();
            logCalculatorUsage('keyboard_backspace');
        } else if (key === 'c' || key === 'C') {
            clear();
            logCalculatorUsage('keyboard_clear');
        }
    }

    function logCalculatorUsage(action, detail = '') {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp: timestamp,
            action: action,
            detail: detail,
            quizId: {{ quiz.id }},
            currentValue: currentInput
        };

        calculatorUsageLog.push(logEntry);

        // Save to sessionStorage
        sessionStorage.setItem('calculatorUsageLog_{{ quiz.id }}', JSON.stringify(calculatorUsageLog));

        // Log to console for debugging (remove in production)
        console.log('Calculator usage:', logEntry);
    }

    function saveCalculatorState() {
        const state = {
            currentInput: currentInput,
            previousInput: previousInput,
            operator: operator,
            waitingForOperand: waitingForOperand,
            isScientificMode: isScientificMode
        };

        sessionStorage.setItem('calculatorState_{{ quiz.id }}', JSON.stringify(state));
    }

    function loadCalculatorState() {
        const savedState = sessionStorage.getItem('calculatorState_{{ quiz.id }}');
        const savedLog = sessionStorage.getItem('calculatorUsageLog_{{ quiz.id }}');

        if (savedState) {
            const state = JSON.parse(savedState);
            currentInput = state.currentInput || '0';
            previousInput = state.previousInput || '';
            operator = state.operator || '';
            waitingForOperand = state.waitingForOperand || false;
            isScientificMode = state.isScientificMode || false;

            updateDisplay();

            if (isScientificMode) {
                scientificCalculator.style.display = 'grid';
                calculatorModeToggle.textContent = 'Basic';
            }
        }

        if (savedLog) {
            calculatorUsageLog = JSON.parse(savedLog);
        }
    }

    // Initialize display
    updateDisplay();
}
{% endif %}

</script>

<style>
.quiz-attempt-container {
    max-width: 900px;
    margin: 2rem auto;
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.quiz-header h1 {
    margin: 0;
    color: #343a40;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#timer {
    font-size: 1.2em;
    font-weight: bold;
    color: #dc3545;
}

.quiz-description {
    background-color: #e9ecef;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 2rem;
    color: #495057;
}

.question-card {
    background-color: #fff;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 0.8rem;
}

.question-number {
    font-weight: bold;
    font-size: 1.1em;
    color: #007bff;
}

.question-marks {
    font-size: 0.9em;
    color: #6c757d;
}

.question-text {
    margin-bottom: 1.2rem;
    font-size: 1.1em;
    color: #212529;
    line-height: 1.6;
}

.options {
    display: flex;
    flex-direction: column;
    gap: 0.8rem; /* Spacing between options */
}

.option-item {
    display: flex; /* Align items for spacing */
    align-items: center; 
}

/* Hide the default radio button */
.option-item input[type="radio"] {
   position: absolute;
   opacity: 0;
   width: 0;
   height: 0;
}

.option-label {
    display: flex; /* Use flex to align text and button */
    align-items: center;
    width: 100%; /* Make label take full width */
    padding: 0.8rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 5px;
    background-color: #fff;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
}

/* Style the label when the radio is checked */
.option-item input[type="radio"]:checked + .option-label {
    background-color: #e2f0ff; /* Light blue background */
    border-color: #007bff;
}

.option-label:hover {
    background-color: #f1f3f5;
}

.option-text {
    flex-grow: 1; /* Allow text to take available space */
    margin-right: 10px; /* Space between text and strike button */
    transition: text-decoration 0.2s;
}

.strike-btn {
    background: none;
    border: 1px solid #adb5bd;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8em;
    line-height: 1;
    margin-left: auto; /* Push button to the right */
}

.strike-btn s {
    text-decoration: none; /* Remove default strikethrough */
}

.strike-btn:hover {
    background-color: #e9ecef;
}

.option-text.strikethrough {
    text-decoration: line-through;
    color: #adb5bd; /* Grey out struck-through text */
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.1em;
    margin-top: 2rem;
}

.strikethrough {
    text-decoration: line-through;
    opacity: 0.6;
}

/* Calculator Styles */
.btn-calculator {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.btn-calculator:hover {
    background: #0056b3;
}

.calculator-icon {
    font-size: 1.1rem;
}

.calculator-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.calculator-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 320px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
}

.calculator-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calculator-header h3 {
    margin: 0;
    color: #495057;
}

.calculator-controls {
    display: flex;
    gap: 0.5rem;
}

.btn-mode {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-mode:hover {
    background: #545b62;
}

.btn-close {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
}

.btn-close:hover {
    background: #c82333;
}

.calculator-body {
    padding: 1rem;
}

.calculator-display {
    margin-bottom: 1rem;
}

.calculator-display input {
    width: 100%;
    padding: 0.75rem;
    font-size: 1.5rem;
    text-align: right;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    background: #f8f9fa;
    color: #495057;
    font-family: 'Courier New', monospace;
}

.calculator-display input:focus {
    outline: none;
    border-color: #007bff;
}

.calculator-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.scientific-grid {
    grid-template-columns: repeat(4, 1fr);
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.calc-btn {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.2s ease;
    user-select: none;
}

.calc-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.calc-btn:active {
    transform: scale(0.95);
}

.calc-number {
    background: #f8f9fa;
    color: #495057;
}

.calc-number:hover {
    background: #e9ecef;
}

.calc-operator {
    background: #e9ecef;
    color: #495057;
}

.calc-operator:hover {
    background: #dee2e6;
}

.calc-equals {
    background: #007bff;
    color: white;
}

.calc-equals:hover {
    background: #0056b3;
}

.calc-clear {
    background: #dc3545;
    color: white;
}

.calc-clear:hover {
    background: #c82333;
}

.calc-function {
    background: #28a745;
    color: white;
    font-size: 0.9rem;
}

.calc-function:hover {
    background: #218838;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-controls {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-end;
    }

    .calculator-container {
        width: 280px;
    }

    .calc-btn {
        padding: 0.6rem;
        font-size: 0.9rem;
    }

    .calculator-display input {
        font-size: 1.3rem;
    }
}

/* Security: Disable text selection and context menu */
.calculator-container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.calculator-container * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

</style>
{% endblock %}