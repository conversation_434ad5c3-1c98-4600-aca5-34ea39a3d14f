#!/usr/bin/env python3
"""
Test script for quiz versioning system.
This script validates that the quiz versioning system works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, QuizAttempt, Question, User, create_quiz_version, has_quiz_attempts, get_latest_quiz_version, get_quiz_family_versions
from werkzeug.security import generate_password_hash

def test_quiz_versioning():
    """Test the quiz versioning system"""
    with app.app_context():
        print("Testing Quiz Versioning System")
        print("=" * 50)
        
        # Create test teacher
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
            db.session.commit()
            print("✓ Created test teacher")
        
        # Create test student
        student = User.query.filter_by(email='<EMAIL>').first()
        if not student:
            student = User(
                name='Test Student',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                is_verified=True
            )
            db.session.add(student)
            db.session.commit()
            print("✓ Created test student")
        
        # Test 1: Create original quiz
        print("\nTest 1: Creating original quiz...")
        original_quiz = Quiz(
            title='Test Quiz for Versioning',
            description='A test quiz to validate versioning',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=50,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False
        )
        db.session.add(original_quiz)
        db.session.flush()
        
        # Add test questions
        question1 = Question(
            quiz_id=original_quiz.id,
            question_text='What is 2+2?',
            question_type='mcq',
            option1='3',
            option2='4',
            option3='5',
            option4='6',
            correct_answer='2',
            marks=25
        )
        question2 = Question(
            quiz_id=original_quiz.id,
            question_text='Is the sky blue?',
            question_type='true_false',
            correct_answer='true',
            marks=25
        )
        db.session.add_all([question1, question2])
        db.session.commit()
        print(f"✓ Created original quiz (ID: {original_quiz.id}, Version: {original_quiz.version_number})")
        
        # Test 2: Verify quiz has no attempts initially
        print("\nTest 2: Checking initial state...")
        assert not has_quiz_attempts(original_quiz.id), "New quiz should have no attempts"
        assert original_quiz.is_active, "New quiz should be active"
        assert not original_quiz.is_locked, "New quiz should not be locked"
        print("✓ Initial state is correct")
        
        # Test 3: Create quiz attempt
        print("\nTest 3: Creating quiz attempt...")
        attempt = QuizAttempt(
            student_id=student.id,
            quiz_id=original_quiz.id,
            score=85.0,
            quiz_version=original_quiz.version_number,
            is_locked=False
        )
        db.session.add(attempt)
        db.session.commit()
        print(f"✓ Created quiz attempt (ID: {attempt.id}, Version: {attempt.quiz_version})")
        
        # Test 4: Verify quiz now has attempts
        print("\nTest 4: Verifying quiz has attempts...")
        assert has_quiz_attempts(original_quiz.id), "Quiz should now have attempts"
        print("✓ Quiz correctly shows it has attempts")
        
        # Test 5: Create new version
        print("\nTest 5: Creating new version...")
        form_data = {
            'quiz_title': 'Test Quiz for Versioning (Updated)',
            'quiz_description': 'Updated description',
            'time_limit': 45,
            'total_marks': 60,
            'grade_a': 85,
            'grade_b': 75,
            'grade_c': 65,
            'grade_d': 55,
            'difficulty': 'hard'
        }
        
        question_data = {
            'question_texts': ['What is 3+3?', 'Is water wet?'],
            'question_marks': [30, 30],
            'mcq_options1': ['5', ''],
            'mcq_options2': ['6', ''],
            'mcq_options3': ['7', ''],
            'mcq_options4': ['8', ''],
            'correct_answer_map': {0: '2', 1: 'true'}
        }
        
        new_quiz = create_quiz_version(original_quiz, form_data, question_data)
        print(f"✓ Created new version (ID: {new_quiz.id}, Version: {new_quiz.version_number})")
        
        # Test 6: Verify original quiz is locked
        print("\nTest 6: Verifying original quiz is locked...")
        db.session.refresh(original_quiz)
        assert original_quiz.is_locked, "Original quiz should be locked"
        assert not original_quiz.is_active, "Original quiz should be inactive"
        assert original_quiz.locked_at is not None, "Original quiz should have lock timestamp"
        print("✓ Original quiz is properly locked")
        
        # Test 7: Verify new quiz is active
        print("\nTest 7: Verifying new quiz is active...")
        assert new_quiz.is_active, "New quiz should be active"
        assert not new_quiz.is_locked, "New quiz should not be locked"
        assert new_quiz.version_number == 2, "New quiz should be version 2"
        assert new_quiz.original_quiz_id == original_quiz.id, "New quiz should reference original"
        print("✓ New quiz is properly configured")
        
        # Test 8: Verify attempt is locked
        print("\nTest 8: Verifying attempt is locked...")
        db.session.refresh(attempt)
        assert attempt.is_locked, "Attempt should be locked when quiz is locked"
        print("✓ Attempt is properly locked")
        
        # Test 9: Test version family functions
        print("\nTest 9: Testing version family functions...")
        latest_version = get_latest_quiz_version(original_quiz.id)
        assert latest_version.id == new_quiz.id, "Latest version should be the new quiz"
        
        all_versions = get_quiz_family_versions(original_quiz.id)
        assert len(all_versions) == 2, "Should have 2 versions in family"
        assert all_versions[0].version_number > all_versions[1].version_number, "Versions should be ordered by version number desc"
        print("✓ Version family functions work correctly")
        
        # Test 10: Test creating another version
        print("\nTest 10: Creating third version...")
        # First create an attempt on the second version
        attempt2 = QuizAttempt(
            student_id=student.id,
            quiz_id=new_quiz.id,
            score=92.0,
            quiz_version=new_quiz.version_number,
            is_locked=False
        )
        db.session.add(attempt2)
        db.session.commit()
        
        # Now create third version
        form_data['quiz_title'] = 'Test Quiz for Versioning (Third Version)'
        third_quiz = create_quiz_version(new_quiz, form_data, question_data)
        print(f"✓ Created third version (ID: {third_quiz.id}, Version: {third_quiz.version_number})")
        
        # Verify version numbers
        assert third_quiz.version_number == 3, "Third quiz should be version 3"
        assert third_quiz.original_quiz_id == original_quiz.id, "Third quiz should reference original"
        
        # Verify second quiz is now locked
        db.session.refresh(new_quiz)
        assert new_quiz.is_locked, "Second quiz should now be locked"
        
        print("✓ Third version created successfully")
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! Quiz versioning system is working correctly.")
        print("\nSummary:")
        print(f"- Original quiz (v1): ID {original_quiz.id} - Locked")
        print(f"- Second quiz (v2): ID {new_quiz.id} - Locked") 
        print(f"- Third quiz (v3): ID {third_quiz.id} - Active")
        print(f"- Total attempts: 2 (both locked)")
        
        return True

if __name__ == "__main__":
    try:
        success = test_quiz_versioning()
        if success:
            print("\n✅ Quiz versioning system test completed successfully!")
        else:
            print("\n❌ Quiz versioning system test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
