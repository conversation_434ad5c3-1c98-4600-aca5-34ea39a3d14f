#!/usr/bin/env python3
"""
Test script for quiz export functionality.
This script validates that the quiz export features work correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from app import app, db, Quiz, Question, User, can_export_quiz, format_question_for_export, generate_quiz_csv_data
from werkzeug.security import generate_password_hash
import tempfile

def test_quiz_export():
    """Test the quiz export functionality"""
    with app.app_context():
        print("Testing Quiz Export Functionality")
        print("=" * 50)
        
        # Create test teacher
        teacher = User.query.filter_by(email='<EMAIL>').first()
        if not teacher:
            teacher = User(
                name='Test Export Teacher',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='teacher',
                is_verified=True
            )
            db.session.add(teacher)
        
        # Create test student (should not be able to export)
        student = User.query.filter_by(email='<EMAIL>').first()
        if not student:
            student = User(
                name='Test Export Student',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='student',
                is_verified=True
            )
            db.session.add(student)
        
        # Create test admin
        admin = User.query.filter_by(email='<EMAIL>').first()
        if not admin:
            admin = User(
                name='Test Export Admin',
                email='<EMAIL>',
                password=generate_password_hash('password123'),
                unhashed_password='password123',
                role='admin',
                is_verified=True
            )
            db.session.add(admin)
        
        db.session.commit()
        print("✓ Created test users (teacher, student, admin)")
        
        # Create test quiz
        test_quiz = Quiz(
            title='Test Export Quiz',
            description='A quiz for testing export functionality',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=100,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='medium',
            version_number=1,
            is_active=True,
            is_locked=False,
            randomize_questions=True,
            allow_calculator=True
        )
        db.session.add(test_quiz)
        db.session.flush()
        
        # Add test questions
        questions = []
        
        # MCQ Question
        mcq_question = Question(
            quiz_id=test_quiz.id,
            question_text='What is 2 + 2?',
            question_type='mcq',
            option1='3',
            option2='4',
            option3='5',
            option4='6',
            correct_answer='2',
            marks=25
        )
        questions.append(mcq_question)
        db.session.add(mcq_question)
        
        # Text Question
        text_question = Question(
            quiz_id=test_quiz.id,
            question_text='What is the capital of France?',
            question_type='text',
            correct_answer='Paris',
            marks=25
        )
        questions.append(text_question)
        db.session.add(text_question)
        
        # Another MCQ Question
        mcq_question2 = Question(
            quiz_id=test_quiz.id,
            question_text='Which planet is closest to the Sun?',
            question_type='mcq',
            option1='Venus',
            option2='Mercury',
            option3='Earth',
            option4='Mars',
            correct_answer='2',
            marks=25
        )
        questions.append(mcq_question2)
        db.session.add(mcq_question2)
        
        # Text Question 2
        text_question2 = Question(
            quiz_id=test_quiz.id,
            question_text='Name the largest ocean on Earth.',
            question_type='text',
            correct_answer='Pacific Ocean',
            marks=25
        )
        questions.append(text_question2)
        db.session.add(text_question2)
        
        db.session.commit()
        print(f"✓ Created test quiz with {len(questions)} questions")
        
        # Test 1: Access control
        print("\nTest 1: Testing access control...")
        
        # Teacher should be able to export their own quiz
        assert can_export_quiz(test_quiz, teacher.id, teacher.role) == True, "Teacher should be able to export their own quiz"
        print("✓ Teacher can export their own quiz")
        
        # Student should not be able to export
        assert can_export_quiz(test_quiz, student.id, student.role) == False, "Student should not be able to export quiz"
        print("✓ Student cannot export quiz")
        
        # Admin should be able to export any quiz
        assert can_export_quiz(test_quiz, admin.id, admin.role) == True, "Admin should be able to export any quiz"
        print("✓ Admin can export any quiz")
        
        # Test 2: Question formatting
        print("\nTest 2: Testing question formatting...")
        
        # Test MCQ formatting
        formatted_mcq = format_question_for_export(mcq_question)
        assert formatted_mcq['question_text'] == 'What is 2 + 2?', "MCQ question text should be preserved"
        assert formatted_mcq['question_type'] == 'mcq', "MCQ question type should be preserved"
        assert formatted_mcq['option2'] == '4', "MCQ options should be preserved"
        assert 'Option 2: 4' in formatted_mcq['correct_answer_display'], "MCQ correct answer should be formatted"
        print("✓ MCQ question formatting works correctly")
        
        # Test text formatting
        formatted_text = format_question_for_export(text_question)
        assert formatted_text['question_text'] == 'What is the capital of France?', "Text question text should be preserved"
        assert formatted_text['question_type'] == 'text', "Text question type should be preserved"
        assert formatted_text['correct_answer_display'] == 'Paris', "Text correct answer should be preserved"
        print("✓ Text question formatting works correctly")
        
        # Test 3: CSV generation
        print("\nTest 3: Testing CSV generation...")
        
        csv_data = generate_quiz_csv_data(test_quiz, questions)
        assert csv_data is not None, "CSV data should be generated"
        assert 'Test Export Quiz' in csv_data, "Quiz title should be in CSV"
        assert 'Test Export Teacher' in csv_data, "Teacher name should be in CSV"
        assert 'What is 2 + 2?' in csv_data, "Question text should be in CSV"
        assert 'Option 2: 4' in csv_data, "Formatted correct answer should be in CSV"
        assert 'Paris' in csv_data, "Text answer should be in CSV"
        print("✓ CSV generation works correctly")
        
        # Test 4: CSV structure validation
        print("\nTest 4: Testing CSV structure...")
        
        csv_lines = csv_data.strip().split('\n')
        assert len(csv_lines) > 10, "CSV should have multiple lines"
        
        # Check for required sections
        csv_content = '\n'.join(csv_lines)
        assert 'Quiz Export' in csv_content, "CSV should have quiz export header"
        assert 'Grading Thresholds' in csv_content, "CSV should have grading thresholds"
        assert 'Questions' in csv_content, "CSV should have questions section"
        print("✓ CSV structure is correct")
        
        # Test 5: File safety
        print("\nTest 5: Testing file name safety...")
        
        # Test with special characters in quiz title
        special_quiz = Quiz(
            title='Test Quiz: "Special" & <Characters>',
            description='Testing special characters',
            teacher_id=teacher.id,
            time_limit=30,
            total_marks=50,
            grade_a_threshold=90,
            grade_b_threshold=80,
            grade_c_threshold=70,
            grade_d_threshold=60,
            difficulty='easy'
        )
        db.session.add(special_quiz)
        db.session.commit()
        
        # Test filename generation (simulated)
        import re
        safe_title = re.sub(r'[^\w\s-]', '', special_quiz.title).strip()
        safe_title = re.sub(r'[-\s]+', '-', safe_title)
        expected_filename = f"quiz-{safe_title}-{special_quiz.id}.csv"
        
        assert 'Test-Quiz-Special-Characters' in expected_filename, "Special characters should be removed from filename"
        print("✓ File name safety works correctly")
        
        # Test 6: Export route simulation
        print("\nTest 6: Testing export route logic...")
        
        with app.test_client() as client:
            # Test without login (should redirect)
            response = client.get(f'/export/quiz/{test_quiz.id}/csv')
            assert response.status_code in [302, 401], "Should require login"
            print("✓ Export routes require authentication")
        
        # Cleanup
        print("\nCleaning up test data...")
        try:
            # Delete questions first (foreign key constraint)
            for question in questions:
                db.session.delete(question)
            
            # Delete quizzes
            db.session.delete(test_quiz)
            db.session.delete(special_quiz)
            
            # Delete test users
            db.session.delete(student)
            db.session.delete(admin)
            db.session.delete(teacher)
            
            db.session.commit()
            print("✓ Test data cleaned up successfully")
        except Exception as e:
            print(f"Warning: Could not clean up all test data: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All quiz export tests passed!")
        print("\nSummary:")
        print("- Access control works correctly")
        print("- Question formatting works for MCQ and text types")
        print("- CSV generation produces valid output")
        print("- CSV structure includes all required sections")
        print("- File name safety handles special characters")
        print("- Export routes require proper authentication")
        
        return True

if __name__ == "__main__":
    try:
        success = test_quiz_export()
        if success:
            print("\n✅ Quiz export test completed successfully!")
        else:
            print("\n❌ Quiz export test failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
