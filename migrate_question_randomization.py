#!/usr/bin/env python3
"""
Migration script to add question randomization fields to Quiz and QuizAttempt tables.
This script adds the randomize_questions field to Quiz table and randomized_question_order field to QuizAttempt table.
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_question_randomization():
    """Add question randomization fields to Quiz and QuizAttempt tables"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure you're running this script from the correct directory.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting question randomization migration...")
        print("=" * 50)
        
        # Check if randomization columns already exist in quiz table
        cursor.execute("PRAGMA table_info(quiz)")
        quiz_columns = [column[1] for column in cursor.fetchall()]
        
        # Check if randomization columns already exist in quiz_attempt table
        cursor.execute("PRAGMA table_info(quiz_attempt)")
        attempt_columns = [column[1] for column in cursor.fetchall()]
        
        quiz_missing_columns = []
        attempt_missing_columns = []
        
        # Check Quiz table
        if 'randomize_questions' not in quiz_columns:
            quiz_missing_columns.append('randomize_questions')
        
        # Check QuizAttempt table
        if 'randomized_question_order' not in attempt_columns:
            attempt_missing_columns.append('randomized_question_order')
        
        if not quiz_missing_columns and not attempt_missing_columns:
            print("Question randomization fields already exist. No migration needed.")
            return
        
        # Add missing columns to Quiz table
        if quiz_missing_columns:
            print(f"Adding missing columns to quiz table: {quiz_missing_columns}")
            
            if 'randomize_questions' in quiz_missing_columns:
                cursor.execute("""
                    ALTER TABLE quiz ADD COLUMN randomize_questions BOOLEAN 
                    DEFAULT 0 NOT NULL
                """)
                print("✓ Added randomize_questions column to quiz table")
        
        # Add missing columns to QuizAttempt table
        if attempt_missing_columns:
            print(f"Adding missing columns to quiz_attempt table: {attempt_missing_columns}")
            
            if 'randomized_question_order' in attempt_missing_columns:
                cursor.execute("""
                    ALTER TABLE quiz_attempt ADD COLUMN randomized_question_order TEXT
                """)
                print("✓ Added randomized_question_order column to quiz_attempt table")
        
        # Update existing data
        print("\nUpdating existing data...")
        
        # Set default values for existing quizzes (randomization disabled by default)
        if 'randomize_questions' in quiz_missing_columns:
            cursor.execute("""
                UPDATE quiz 
                SET randomize_questions = 0 
                WHERE randomize_questions IS NULL
            """)
            print("✓ Set default randomization settings for existing quizzes")
        
        # Set randomized_question_order to NULL for existing attempts (no randomization)
        if 'randomized_question_order' in attempt_missing_columns:
            cursor.execute("""
                UPDATE quiz_attempt 
                SET randomized_question_order = NULL 
                WHERE randomized_question_order IS NULL
            """)
            print("✓ Set default question order for existing attempts")
        
        conn.commit()
        print("\n✓ Database migration completed successfully!")
        print("\nQuestion randomization is now enabled:")
        print("- Teachers can enable/disable randomization when creating quizzes")
        print("- Each student gets a unique question order per quiz attempt")
        print("- Question order is preserved for result viewing")
        print("- Existing quizzes have randomization disabled by default")
        
        # Show statistics
        cursor.execute("SELECT COUNT(*) FROM quiz")
        total_quizzes = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM quiz WHERE randomize_questions = 1")
        randomized_quizzes = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM quiz_attempt")
        total_attempts = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM quiz_attempt WHERE randomized_question_order IS NOT NULL")
        randomized_attempts = cursor.fetchone()[0]
        
        print(f"\nDatabase Statistics:")
        print(f"- Total quizzes: {total_quizzes}")
        print(f"- Quizzes with randomization enabled: {randomized_quizzes}")
        print(f"- Total quiz attempts: {total_attempts}")
        print(f"- Attempts with randomized questions: {randomized_attempts}")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nVerifying migration...")
        
        # Check Quiz table structure
        cursor.execute("PRAGMA table_info(quiz)")
        quiz_columns = [column[1] for column in cursor.fetchall()]
        
        # Check QuizAttempt table structure
        cursor.execute("PRAGMA table_info(quiz_attempt)")
        attempt_columns = [column[1] for column in cursor.fetchall()]
        
        # Verify required columns exist
        required_quiz_columns = ['randomize_questions']
        required_attempt_columns = ['randomized_question_order']
        
        quiz_check = all(col in quiz_columns for col in required_quiz_columns)
        attempt_check = all(col in attempt_columns for col in required_attempt_columns)
        
        if quiz_check and attempt_check:
            print("✅ Migration verification successful!")
            print("All required columns are present.")
            return True
        else:
            print("❌ Migration verification failed!")
            if not quiz_check:
                missing = [col for col in required_quiz_columns if col not in quiz_columns]
                print(f"Missing Quiz columns: {missing}")
            if not attempt_check:
                missing = [col for col in required_attempt_columns if col not in attempt_columns]
                print(f"Missing QuizAttempt columns: {missing}")
            return False
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Question Randomization Migration")
    print("=" * 50)
    migrate_question_randomization()
    verify_migration()
    print("=" * 50)
    print("Migration completed!")
