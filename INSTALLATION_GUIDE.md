# Flask Quiz Management System - Installation Guide

## 🎯 Complete Setup Instructions

This guide will help you install all required dependencies and set up the Flask Quiz Management System on your Windows machine.

## ✅ Prerequisites

### 1. Python Installation
- **Download Python**: Go to https://python.org/downloads/
- **Version**: Python 3.11 or 3.12 (tested with Python 3.13)
- **Important**: During installation, check "Add Python to PATH"
- **Verify Installation**:
  ```cmd
  python --version
  pip --version
  ```

### 2. Project Setup
```cmd
# Navigate to your project directory
cd C:\Users\<USER>\Desktop\Quiz_management_system

# Verify you're in the correct directory
dir
```

## 📦 Dependencies Installation

### Method 1: Install from requirements.txt (Recommended)
```cmd
pip install -r requirements.txt
```

### Method 2: Manual Installation (if requirements.txt fails)
```cmd
# Core Flask packages
pip install Flask==2.3.3 Werkzeug==2.3.7 Flask-SQLAlchemy==3.1.1 Flask-Mail==0.9.1

# PDF generation for exports
pip install xhtml2pdf==0.2.17

# Form handling
pip install Flask-WTF==1.2.2 WTForms==3.2.1

# Utilities
pip install python-dateutil==2.8.2 requests==2.31.0 python-dotenv==1.1.1
```

## 🔧 Installed Packages Overview

### Core Flask Framework
- **Flask 2.3.3**: Main web framework
- **Werkzeug 2.3.7**: WSGI utilities and security
- **Jinja2 3.1.6**: Template engine
- **Flask-SQLAlchemy 3.1.1**: Database ORM
- **Flask-Mail 0.9.1**: Email functionality

### PDF Export Features
- **xhtml2pdf 0.2.17**: HTML to PDF conversion
- **reportlab 4.4.2**: PDF generation library
- **Pillow 11.3.0**: Image processing
- **pypdf 5.7.0**: PDF manipulation

### Form Handling
- **Flask-WTF 1.2.2**: Form validation and CSRF protection
- **WTForms 3.2.1**: Form rendering and validation

### Security & Cryptography
- **cryptography 45.0.5**: Encryption and security
- **pyHanko 0.29.1**: PDF signing capabilities

### Utilities
- **python-dateutil 2.8.2**: Date/time utilities
- **requests 2.31.0**: HTTP requests
- **python-dotenv 1.1.1**: Environment variables

## 🚀 Running the Application

### 1. Start the Flask Application
```cmd
python app.py
```

### 2. Access the Application
- **URL**: http://localhost:5000
- **Default Port**: 5000
- **Admin Login**: Check the console output for admin credentials

### 3. Verify Installation
```cmd
# Test import
python -c "import app; print('App imported successfully')"

# Check installed packages
pip list
```

## 🎯 Key Features Available

### ✅ Core Functionality
- **User Management**: Students, teachers, parents, admins
- **Quiz System**: Create, edit, take quizzes with versioning
- **Grading System**: Automatic grading with customizable thresholds
- **Messaging System**: Communication between users
- **Report Cards**: Comprehensive student performance tracking

### ✅ Advanced Features
- **Quiz Export**: CSV and PDF export for teachers
- **Report Card Export**: PDF export for students' academic records
- **Statistics Dashboard**: Beautiful charts and analytics
- **Parent Feedback**: Parent comments on student performance
- **Teacher Comments**: Overall and quiz-specific feedback
- **Calculator Integration**: Optional calculator for quizzes
- **Question Randomization**: Randomize question order per student

### ✅ Modern UI Features
- **Responsive Design**: Works on all device sizes
- **Interactive Charts**: Chart.js integration for statistics
- **Modern Styling**: Gradient backgrounds and animations
- **Export Functionality**: Professional PDF and CSV exports
- **Role-based Access**: Secure access control for all features

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. Python Not Found Error
```
Error: Python was not found
Solution: Reinstall Python with "Add to PATH" checked
```

#### 2. Package Installation Fails
```
Error: Failed building wheel for [package]
Solution: Try installing packages individually:
pip install Flask Flask-SQLAlchemy Flask-Mail xhtml2pdf
```

#### 3. PDF Export Not Working
```
Error: PDF export is not available
Solution: Ensure xhtml2pdf is installed:
pip install xhtml2pdf
```

#### 4. Database Issues
```
Error: Database connection failed
Solution: The app uses SQLite by default (no setup required)
For MySQL: pip install mysql-connector-python
```

#### 5. Port Already in Use
```
Error: Address already in use
Solution: Kill existing process or use different port:
python app.py --port 5001
```

## 📁 Project Structure

```
Quiz_management_system/
├── app.py                          # Main Flask application
├── requirements.txt                # Dependencies list
├── quiz_management.db              # SQLite database (auto-created)
├── templates/                      # HTML templates
│   ├── exports/                    # Export templates
│   │   ├── quiz_pdf.html          # Quiz PDF template
│   │   └── report_card_pdf.html   # Report card PDF template
│   └── ...                        # Other templates
├── static/                         # CSS, JS, images
├── test_*.py                       # Test scripts
├── demo_*.py                       # Demo scripts
└── *.md                           # Documentation files
```

## 🧪 Testing the Installation

### 1. Run Test Scripts
```cmd
# Test quiz export functionality
python test_quiz_export.py

# Test report card export functionality
python test_report_card_export.py

# Run demo scripts
python demo_quiz_export.py
python demo_report_card_export.py
```

### 2. Test Core Features
1. **Login**: Access http://localhost:5000 and login
2. **Create Quiz**: Test quiz creation as a teacher
3. **Take Quiz**: Test quiz taking as a student
4. **Export**: Test PDF/CSV export functionality
5. **Reports**: Test report card generation

## 🔒 Security Notes

### Default Admin Account
- **Username**: Check console output when starting the app
- **Password**: Check console output when starting the app
- **Change**: Update admin credentials after first login

### Security Features
- **Password Hashing**: Werkzeug secure password hashing
- **CSRF Protection**: Flask-WTF CSRF tokens
- **Role-based Access**: Secure route protection
- **Input Validation**: Form validation and sanitization

## 🎉 Success Indicators

### ✅ Installation Successful When:
1. **Python Import Works**: `python -c "import app"` runs without errors
2. **Flask Starts**: `python app.py` starts without errors
3. **Web Access**: http://localhost:5000 loads the login page
4. **Database Created**: `quiz_management.db` file appears
5. **Admin User**: Console shows admin user creation
6. **PDF Export**: Export buttons work without errors

### ✅ All Features Working When:
- Login system works for all user roles
- Quiz creation and taking functions properly
- PDF exports download successfully
- Statistics dashboard displays charts
- Messaging system sends/receives messages
- Report cards generate with student data

## 📞 Support

### If You Encounter Issues:
1. **Check Python Version**: Ensure Python 3.11+ is installed
2. **Verify PATH**: Python should be in system PATH
3. **Check Dependencies**: Run `pip list` to verify installations
4. **Review Logs**: Check console output for error messages
5. **Test Individual Components**: Use test scripts to isolate issues

### Common Commands Reference:
```cmd
# Check Python
python --version

# Install dependencies
pip install -r requirements.txt

# Run application
python app.py

# Test import
python -c "import app; print('Success')"

# List packages
pip list

# Upgrade pip
python -m pip install --upgrade pip
```

## 🎯 Next Steps

After successful installation:
1. **Explore Features**: Login and test all functionality
2. **Create Content**: Add quizzes, users, and test data
3. **Customize**: Modify templates and styling as needed
4. **Deploy**: Consider deployment options for production use
5. **Backup**: Regular database backups for important data

Your Flask Quiz Management System is now ready to use with all advanced features including quiz exports, report card generation, statistics dashboard, and comprehensive user management!
