#!/usr/bin/env python3
"""
Migration script to add calculator feature to Quiz table.
This script adds the allow_calculator field to the Quiz table for the calculator feature.
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_calculator_feature():
    """Add allow_calculator field to Quiz table"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure you're running this script from the correct directory.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Starting calculator feature migration...")
        print("=" * 50)
        
        # Check if allow_calculator column already exists in quiz table
        cursor.execute("PRAGMA table_info(quiz)")
        quiz_columns = [column[1] for column in cursor.fetchall()]
        
        if 'allow_calculator' in quiz_columns:
            print("Calculator feature field already exists. No migration needed.")
            return
        
        print("Adding allow_calculator field to quiz table...")
        
        # Add allow_calculator column to quiz table
        cursor.execute("""
            ALTER TABLE quiz ADD COLUMN allow_calculator BOOLEAN 
            DEFAULT 1 NOT NULL
        """)
        print("✓ Added allow_calculator column to quiz table")
        
        # Update existing quizzes to have calculator enabled by default
        cursor.execute("""
            UPDATE quiz 
            SET allow_calculator = 1 
            WHERE allow_calculator IS NULL
        """)
        print("✓ Set default calculator access for existing quizzes")
        
        conn.commit()
        print("\n✓ Database migration completed successfully!")
        print("\nCalculator feature is now enabled:")
        print("- Teachers can enable/disable calculator access when creating quizzes")
        print("- Students get access to a built-in calculator during quiz attempts")
        print("- Calculator is fully client-side and secure")
        print("- Calculator usage is logged for monitoring")
        print("- Existing quizzes have calculator access enabled by default")
        
        # Show statistics
        cursor.execute("SELECT COUNT(*) FROM quiz")
        total_quizzes = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM quiz WHERE allow_calculator = 1")
        calculator_enabled_quizzes = cursor.fetchone()[0]
        
        print(f"\nDatabase Statistics:")
        print(f"- Total quizzes: {total_quizzes}")
        print(f"- Quizzes with calculator enabled: {calculator_enabled_quizzes}")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nVerifying migration...")
        
        # Check Quiz table structure
        cursor.execute("PRAGMA table_info(quiz)")
        quiz_columns = [column[1] for column in cursor.fetchall()]
        
        # Verify required column exists
        if 'allow_calculator' not in quiz_columns:
            print("❌ Migration verification failed!")
            print("Missing allow_calculator column in quiz table.")
            return False
        
        # Check column properties
        cursor.execute("PRAGMA table_info(quiz)")
        columns = cursor.fetchall()
        
        allow_calculator_column = None
        for column in columns:
            if column[1] == 'allow_calculator':
                allow_calculator_column = column
                break
        
        if allow_calculator_column:
            column_type = allow_calculator_column[2]
            not_null = allow_calculator_column[3]
            default_value = allow_calculator_column[4]
            
            print(f"✓ allow_calculator column found:")
            print(f"  - Type: {column_type}")
            print(f"  - Not Null: {bool(not_null)}")
            print(f"  - Default: {default_value}")
        
        print("✅ Migration verification successful!")
        print("Calculator feature is properly configured.")
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_calculator_feature():
    """Test basic calculator feature functionality"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nTesting calculator feature functionality...")
        
        # Test querying quizzes with calculator settings
        cursor.execute("""
            SELECT id, title, allow_calculator FROM quiz 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        if results:
            print("✓ Quiz calculator settings query works correctly")
            print("Sample quiz calculator settings:")
            for quiz_id, title, allow_calc in results:
                calc_status = "Enabled" if allow_calc else "Disabled"
                print(f"  - Quiz {quiz_id}: '{title[:30]}...' - Calculator: {calc_status}")
        else:
            print("ℹ️ No quizzes found in database")
        
        # Test updating calculator setting
        cursor.execute("""
            SELECT id FROM quiz LIMIT 1
        """)
        
        test_quiz = cursor.fetchone()
        if test_quiz:
            quiz_id = test_quiz[0]
            
            # Test disabling calculator
            cursor.execute("""
                UPDATE quiz 
                SET allow_calculator = 0 
                WHERE id = ?
            """, (quiz_id,))
            
            # Test enabling calculator
            cursor.execute("""
                UPDATE quiz 
                SET allow_calculator = 1 
                WHERE id = ?
            """, (quiz_id,))
            
            print("✓ Calculator setting update operations work correctly")
        
        conn.rollback()  # Don't actually save test changes
        print("✅ All basic operations are functional!")
        
    except Exception as e:
        print(f"❌ Error during functionality test: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Calculator Feature Migration")
    print("=" * 50)
    migrate_calculator_feature()
    verify_migration()
    test_calculator_feature()
    print("=" * 50)
    print("Migration completed!")
